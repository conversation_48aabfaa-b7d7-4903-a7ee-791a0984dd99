# Tutorial File Scanner 📚

A comprehensive Python script to scan for tutorial files across multiple SSH servers and local drives, generating detailed inventory reports with duplicate detection and metadata extraction.

## 🚀 Features

### Multi-Location Scanning
- **SSH Servers**: Connect to remote servers via SSH/SFTP
- **Local Drives**: Scan local directories and mounted drives
- **Password Authentication**: Secure password-based SSH authentication (SSH keys disabled)
- **Concurrent Processing**: Efficient scanning with progress tracking

### Comprehensive File Analysis
- **Metadata Extraction**: File size, modification date, content type
- **Duplicate Detection**: SHA/MD5 checksums for identifying duplicates
- **File Type Detection**: MIME type detection and extension analysis
- **Human-Readable Sizes**: Automatic size formatting (B, KB, MB, GB)

### Robust Reporting
- **CSV Reports**: Detailed inventory with custom separators
- **Duplicate Reports**: Separate report for duplicate file groups
- **Summary Statistics**: Per-source statistics and totals
- **Error Logging**: Comprehensive error tracking and reporting

### Advanced Configuration
- **JSON Configuration**: Easy-to-edit configuration files
- **Password-Only Auth**: Simplified SSH authentication without key management
- **Flexible File Types**: Configurable file extensions
- **Performance Tuning**: Adjustable timeouts and chunk sizes
- **Custom Separators**: Avoid CSV parsing issues with file names

## 📋 Requirements

### Python Dependencies
```bash
pip install paramiko tqdm
```

### System Requirements
- Python 3.7+
- SSH access to remote servers (if using SSH functionality)
- Sufficient disk space for reports and logs

## 🛠️ Quick Setup

### 1. Automated Setup
```bash
# Run the setup script for guided installation
python setup_tutorial_scanner.py
```

### 2. Manual Setup
```bash
# Install dependencies
pip install paramiko tqdm

# Create sample configuration
python tutorial_scanner.py --create-config

# Edit the configuration file
nano tutorial_scanner_config.json
```

## ⚙️ Configuration

### Sample Configuration File
```json
{
  "ssh_servers": [
    {
      "hostname": "server1.example.com",
      "username": "your_username",
      "key_file": "/home/<USER>/.ssh/id_rsa",
      "port": 22,
      "directories": [
        "/home/<USER>/tutorials",
        "/shared/learning_materials"
      ]
    },
    {
      "hostname": "*************",
      "username": "admin",
      "password": "your_password",
      "directories": ["/media/tutorials"]
    }
  ],
  "local_directories": [
    "/home/<USER>/Downloads",
    "/media/external_drive/tutorials",
    "C:\\Users\\<USER>\\Documents\\Tutorials"
  ],
  "file_extensions": [
    ".pdf", ".mp4", ".mkv", ".avi", ".mov",
    ".txt", ".md", ".docx", ".pptx", ".epub"
  ],
  "output_file": "tutorial_inventory.csv",
  "separator": "|~|",
  "hash_algorithm": "md5",
  "max_file_size_mb": 10000,
  "ssh_timeout": 30
}
```

### Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `ssh_servers` | List of SSH server configurations | `[]` |
| `local_directories` | Local directories to scan | `["."]` |
| `file_extensions` | File extensions to search for | Common tutorial formats |
| `output_file` | Main CSV report filename | `tutorial_inventory.csv` |
| `separator` | CSV field separator | `|~|` |
| `hash_algorithm` | Hash algorithm (md5/sha1/sha256) | `md5` |
| `max_file_size_mb` | Max file size for hashing (MB) | `10000` |
| `ssh_timeout` | SSH connection timeout (seconds) | `30` |

## 🔐 SSH Setup

### 1. Generate SSH Key Pair
```bash
# Generate new SSH key
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Default location: ~/.ssh/id_rsa
```

### 2. Copy Public Key to Servers
```bash
# Method 1: Using ssh-copy-id
ssh-copy-id username@server_hostname

# Method 2: Manual copy
cat ~/.ssh/id_rsa.pub
# Then add to ~/.ssh/authorized_keys on remote server
```

### 3. Test Connection
```bash
ssh -i ~/.ssh/id_rsa username@server_hostname
```

## 🚀 Usage

### Basic Usage
```bash
# Scan with default configuration
python tutorial_scanner.py

# Scan with custom configuration
python tutorial_scanner.py --config my_config.json

# Scan only local directories
python tutorial_scanner.py --local-only

# Custom output file
python tutorial_scanner.py --output my_inventory.csv
```

### Advanced Usage
```bash
# Create configuration file
python tutorial_scanner.py --create-config

# Use custom separator
python tutorial_scanner.py --separator "###"

# Interactive setup
python setup_tutorial_scanner.py
```

## 📊 Output Files

### Main Inventory Report (`tutorial_inventory.csv`)
```csv
filename|~|full_path|~|source|~|file_size_bytes|~|file_size_mb|~|file_size_human|~|file_extension|~|content_type|~|modification_date|~|checksum|~|hash_algorithm|~|is_duplicate
Python Tutorial.pdf|~|/tutorials/Python Tutorial.pdf|~|server1|~|2048576|~|2.0|~|2.0 MB|~|.pdf|~|application/pdf|~|2024-01-15 10:30:00|~|d41d8cd98f00b204e9800998ecf8427e|~|md5|~|False
```

### Duplicates Report (`tutorial_inventory_duplicates.csv`)
```csv
checksum|~|duplicate_count|~|total_size_mb|~|file_details
d41d8cd98f00b204e9800998ecf8427e|~|3|~|6.0|~|server1:/tutorials/file1.pdf (2.0 MB) ¦¦¦ local:/downloads/file1.pdf (2.0 MB) ¦¦¦ server2:/backup/file1.pdf (2.0 MB)
```

### Log File (`tutorial_scanner.log`)
```
2024-01-15 10:30:00,123 - INFO - Starting tutorial file scan
2024-01-15 10:30:01,456 - INFO - Connecting to SSH server: server1.example.com
2024-01-15 10:30:02,789 - INFO - Found 156 tutorial files on server1.example.com
```

## 📈 Example Output

```
TUTORIAL FILE SCAN SUMMARY
==================================================
Total files found: 1,247
Total size: 15.3 GB
Duplicate groups: 23
Duplicate files: 67
Wasted space: 2.1 GB
Errors encountered: 3

Per-source statistics:
  local:
    Files: 423
    Size: 5.2 GB
    Errors: 1
  server1.example.com:
    Files: 567
    Size: 7.8 GB
    Errors: 1
  server2.example.com:
    Files: 257
    Size: 2.3 GB
    Errors: 1
```

## 🔧 Troubleshooting

### Common Issues

**SSH Connection Failed**
```bash
# Test SSH connection manually
ssh -v username@hostname

# Check SSH key permissions
chmod 600 ~/.ssh/id_rsa
chmod 700 ~/.ssh
```

**Permission Denied Errors**
- Check file/directory permissions on remote servers
- Ensure SSH user has read access to target directories
- Verify SSH key is properly configured

**Large File Handling**
- Files larger than `max_file_size_mb` skip hash calculation
- Adjust `max_file_size_mb` in configuration if needed
- Monitor memory usage for very large files

**CSV Parsing Issues**
- Use custom separator (default `|~|`) to avoid comma conflicts
- File names with commas are handled automatically
- Use CSV reader utility for proper parsing

### Performance Optimization

**For Large Datasets:**
- Increase `chunk_size` for faster file reading
- Reduce `max_file_size_mb` to skip large files
- Use faster hash algorithm (`md5` vs `sha256`)

**For Slow Networks:**
- Increase `ssh_timeout` for slow connections
- Process servers sequentially vs concurrently
- Use compression in SSH config

## 🔍 Reading CSV Reports

### Using the CSV Reader Utility
```bash
# Read main report
python csv_reader_utility.py tutorial_inventory.csv --analyze

# Read duplicates report
python csv_reader_utility.py tutorial_inventory_duplicates.csv --duplicates

# Use pandas for analysis
python csv_reader_utility.py tutorial_inventory.csv --pandas
```

### Using Python
```python
import pandas as pd

# Read with custom separator
df = pd.read_csv('tutorial_inventory.csv', sep='|~|')

# Analysis
print(f"Total files: {len(df)}")
print(f"Total size: {df['file_size_mb'].sum():.1f} MB")
print(f"Duplicates: {df['is_duplicate'].sum()}")

# Find largest files
largest = df.nlargest(10, 'file_size_mb')
print(largest[['filename', 'source', 'file_size_human']])
```

## 📝 File Types Supported

### Default Extensions
- **Documents**: `.pdf`, `.txt`, `.md`, `.rst`, `.docx`, `.pptx`, `.epub`
- **Videos**: `.mp4`, `.mkv`, `.avi`, `.mov`, `.wmv`, `.flv`
- **Archives**: `.zip`, `.rar`, `.7z`, `.tar.gz`
- **Images**: `.jpg`, `.png`, `.gif` (if configured)

### Custom Extensions
Add any file extensions to the configuration:
```json
"file_extensions": [".pdf", ".mp4", ".custom_ext"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is open source. Feel free to use, modify, and distribute.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the log files
3. Test SSH connections manually
4. Create an issue with detailed error information

---

**Happy Tutorial Hunting! 📚🔍**
