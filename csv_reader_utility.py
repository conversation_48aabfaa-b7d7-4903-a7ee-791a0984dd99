#!/usr/bin/env python3
"""
CSV Reader Utility for PDF Scanner Output

This utility helps read CSV files generated by the PDF scanner with custom separators.
It provides functions to read and analyze the CSV data properly.
"""

import csv
import pandas as pd
import argparse
from pathlib import Path
from typing import List, Dict, Optional
import json


def read_pdf_csv(file_path: str, separator: str = '|~|') -> List[Dict[str, str]]:
    """
    Read PDF scanner CSV file with custom separator.
    
    Args:
        file_path (str): Path to the CSV file
        separator (str): Separator used in the CSV file
    
    Returns:
        List[Dict[str, str]]: List of PDF file records
    """
    records = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile, delimiter=separator)
            for row in reader:
                records.append(row)
        
        print(f"Successfully read {len(records)} records from {file_path}")
        return records
        
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return []


def read_duplicates_csv(file_path: str, separator: str = '|~|') -> List[Dict[str, str]]:
    """
    Read duplicates CSV file with custom separator.
    
    Args:
        file_path (str): Path to the duplicates CSV file
        separator (str): Separator used in the CSV file
    
    Returns:
        List[Dict[str, str]]: List of duplicate group records
    """
    records = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile, delimiter=separator)
            for row in reader:
                # Split the file paths using the internal separator
                if 'file_paths' in row:
                    row['file_paths_list'] = [path.strip() for path in row['file_paths'].split(' ¦¦¦ ')]
                records.append(row)
        
        print(f"Successfully read {len(records)} duplicate groups from {file_path}")
        return records
        
    except Exception as e:
        print(f"Error reading duplicates CSV file: {e}")
        return []


def read_with_pandas(file_path: str, separator: str = '|~|') -> Optional[pd.DataFrame]:
    """
    Read CSV file using pandas with custom separator.
    
    Args:
        file_path (str): Path to the CSV file
        separator (str): Separator used in the CSV file
    
    Returns:
        Optional[pd.DataFrame]: DataFrame containing the data
    """
    try:
        df = pd.read_csv(file_path, sep=separator, encoding='utf-8')
        print(f"Successfully loaded DataFrame with {len(df)} rows and {len(df.columns)} columns")
        return df
        
    except Exception as e:
        print(f"Error reading CSV with pandas: {e}")
        return None


def analyze_pdf_data(records: List[Dict[str, str]]) -> Dict[str, any]:
    """
    Analyze PDF data and provide statistics.
    
    Args:
        records (List[Dict[str, str]]): PDF file records
    
    Returns:
        Dict[str, any]: Analysis results
    """
    if not records:
        return {}
    
    # Convert size strings to integers for analysis
    sizes = []
    for record in records:
        try:
            size = int(record.get('size', 0))
            if size > 0:
                sizes.append(size)
        except (ValueError, TypeError):
            continue
    
    total_size = sum(sizes)
    avg_size = total_size / len(sizes) if sizes else 0
    
    # File extension analysis
    extensions = {}
    for record in records:
        filename = record.get('filename', '')
        if '.' in filename:
            ext = filename.split('.')[-1].lower()
            extensions[ext] = extensions.get(ext, 0) + 1
    
    # Date analysis (if available)
    years = {}
    for record in records:
        date_str = record.get('modified_date', '')
        if date_str and date_str != 'Unknown':
            try:
                year = date_str.split('-')[0]
                years[year] = years.get(year, 0) + 1
            except:
                continue
    
    return {
        'total_files': len(records),
        'total_size_bytes': total_size,
        'total_size_mb': round(total_size / (1024 * 1024), 2),
        'average_size_mb': round(avg_size / (1024 * 1024), 2),
        'largest_file_size': max(sizes) if sizes else 0,
        'smallest_file_size': min(sizes) if sizes else 0,
        'file_extensions': extensions,
        'files_by_year': years
    }


def convert_to_standard_csv(input_file: str, output_file: str, separator: str = '|~|') -> bool:
    """
    Convert custom separator CSV to standard comma-separated CSV.
    Note: This may cause issues if filenames contain commas.
    
    Args:
        input_file (str): Input CSV file with custom separator
        output_file (str): Output CSV file with comma separator
        separator (str): Current separator in the input file
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        records = read_pdf_csv(input_file, separator)
        if not records:
            return False
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            if records:
                fieldnames = records[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(records)
        
        print(f"Converted {len(records)} records to standard CSV: {output_file}")
        print("⚠️  Warning: Standard CSV may have issues with filenames containing commas")
        return True
        
    except Exception as e:
        print(f"Error converting to standard CSV: {e}")
        return False


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(
        description="Utility to read and analyze PDF scanner CSV files with custom separators"
    )
    parser.add_argument(
        "csv_file",
        help="Path to the CSV file to read"
    )
    parser.add_argument(
        "--separator",
        default="|~|",
        help="Separator used in the CSV file (default: |~|)"
    )
    parser.add_argument(
        "--analyze",
        action="store_true",
        help="Perform analysis on the data"
    )
    parser.add_argument(
        "--convert",
        help="Convert to standard CSV (specify output filename)"
    )
    parser.add_argument(
        "--duplicates",
        action="store_true",
        help="Read as duplicates CSV file"
    )
    parser.add_argument(
        "--pandas",
        action="store_true",
        help="Use pandas to read and display basic info"
    )
    
    args = parser.parse_args()
    
    if not Path(args.csv_file).exists():
        print(f"Error: File '{args.csv_file}' does not exist.")
        return 1
    
    print(f"Reading CSV file: {args.csv_file}")
    print(f"Using separator: '{args.separator}'")
    print("-" * 50)
    
    if args.pandas:
        # Use pandas
        df = read_with_pandas(args.csv_file, args.separator)
        if df is not None:
            print("\nDataFrame Info:")
            print(df.info())
            print("\nFirst 5 rows:")
            print(df.head())
            
            if 'size_mb' in df.columns:
                print(f"\nSize statistics:")
                print(df['size_mb'].describe())
    
    elif args.duplicates:
        # Read as duplicates file
        records = read_duplicates_csv(args.csv_file, args.separator)
        if records:
            print(f"\nFound {len(records)} duplicate groups:")
            for i, record in enumerate(records[:5], 1):  # Show first 5 groups
                print(f"\nGroup {i}:")
                print(f"  Checksum: {record['checksum'][:16]}...")
                print(f"  Files: {record['duplicate_count']}")
                if 'file_paths_list' in record:
                    for path in record['file_paths_list'][:3]:  # Show first 3 files
                        print(f"    - {Path(path).name}")
                    if len(record['file_paths_list']) > 3:
                        print(f"    ... and {len(record['file_paths_list']) - 3} more")
            
            if len(records) > 5:
                print(f"\n... and {len(records) - 5} more duplicate groups")
    
    else:
        # Read as regular PDF CSV
        records = read_pdf_csv(args.csv_file, args.separator)
        
        if records and args.analyze:
            print("\nPerforming analysis...")
            analysis = analyze_pdf_data(records)
            
            print(f"\nAnalysis Results:")
            print(f"Total files: {analysis['total_files']}")
            print(f"Total size: {analysis['total_size_mb']} MB")
            print(f"Average size: {analysis['average_size_mb']} MB")
            print(f"Largest file: {analysis['largest_file_size'] / (1024*1024):.2f} MB")
            print(f"Smallest file: {analysis['smallest_file_size'] / (1024*1024):.2f} MB")
            
            if analysis['file_extensions']:
                print(f"\nFile extensions:")
                for ext, count in sorted(analysis['file_extensions'].items()):
                    print(f"  .{ext}: {count} files")
            
            if analysis['files_by_year']:
                print(f"\nFiles by year:")
                for year, count in sorted(analysis['files_by_year'].items()):
                    print(f"  {year}: {count} files")
        
        elif records:
            print(f"\nShowing first 5 records:")
            for i, record in enumerate(records[:5], 1):
                print(f"\nRecord {i}:")
                print(f"  Filename: {record.get('filename', 'N/A')}")
                print(f"  Size: {record.get('size_mb', 'N/A')} MB")
                print(f"  Modified: {record.get('modified_date', 'N/A')}")
                print(f"  Path: {record.get('relative_path', 'N/A')}")
    
    if args.convert:
        print(f"\nConverting to standard CSV: {args.convert}")
        success = convert_to_standard_csv(args.csv_file, args.convert, args.separator)
        if not success:
            return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
