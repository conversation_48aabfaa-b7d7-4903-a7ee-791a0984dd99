#!/usr/bin/env python3
"""
Demonstration of unique output filename generation for Tutorial Scanner.

This script shows how the unique filename generation works and demonstrates
running multiple scans without file conflicts.
"""

import os
import tempfile
import shutil
import json
import time
from pathlib import Path
from tutorial_scanner import TutorialScanner, generate_unique_filename


def demo_unique_filename_generation():
    """Demonstrate unique filename generation."""
    print("="*60)
    print("UNIQUE FILENAME GENERATION DEMO")
    print("="*60)
    
    base_filenames = [
        "tutorial_inventory.csv",
        "my_scan_results.csv",
        "data_analysis.csv"
    ]
    
    print("Generating unique filenames:")
    print("-" * 40)
    
    for base_name in base_filenames:
        print(f"\nBase filename: {base_name}")
        
        # Generate multiple unique versions
        for i in range(3):
            unique_name = generate_unique_filename(base_name, include_hostname=True)
            print(f"  Unique {i+1}: {unique_name}")
            time.sleep(0.1)  # Small delay to show timestamp differences
    
    print("\nFilename components:")
    print("- Original name")
    print("- Timestamp (YYYYMMDD_HHMMSS)")
    print("- Hostname (short)")
    print("- UUID (8 characters)")
    print("- Original extension")


def create_demo_files():
    """Create demo files for testing."""
    test_dir = tempfile.mkdtemp(prefix="unique_output_demo_")
    print(f"\nCreating demo files in: {test_dir}")
    
    # Create some sample tutorial files
    demo_files = [
        ("Python_Tutorial.pdf", "Python programming tutorial content"),
        ("JavaScript_Guide.mp4", "JavaScript video tutorial content" * 50),
        ("Design_Basics.txt", "Design fundamentals text content"),
        ("Math_Course.epub", "Mathematics course ebook content"),
        ("Language_Learning.md", "Language learning markdown content")
    ]
    
    for filename, content in demo_files:
        file_path = os.path.join(test_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  Created: {filename}")
    
    return test_dir


def demo_multiple_scans():
    """Demonstrate running multiple scans with unique outputs."""
    print("\n" + "="*60)
    print("MULTIPLE SCANS DEMO")
    print("="*60)
    
    # Create demo files
    test_dir = create_demo_files()
    
    try:
        # Configuration for demo scans
        base_config = {
            "ssh_servers": [],
            "local_directories": [test_dir],
            "file_extensions": [".pdf", ".mp4", ".txt", ".epub", ".md"],
            "output_file": "demo_scan_results.csv",
            "separator": "|~|",
            "hash_algorithm": "md5",
            "parallel_processing": True,
            "log_level": "WARNING"  # Reduce log noise
        }
        
        # Run multiple scans
        scan_results = []
        
        for i in range(3):
            print(f"\n--- Running Scan {i+1} ---")
            
            # Create temporary config file
            config_file = f"demo_config_{i+1}.json"
            with open(config_file, 'w') as f:
                json.dump(base_config, f, indent=2)
            
            try:
                # Run scan with unique output enabled (default)
                scanner = TutorialScanner(config_file, unique_output=True)
                
                print(f"Scan ID: {scanner.scan_id}")
                print(f"Output file: {scanner.config['output_file']}")
                
                # Perform scan
                scanner.scan_all()
                
                scan_results.append({
                    'scan_id': scanner.scan_id,
                    'output_file': scanner.config['output_file'],
                    'files_found': len(scanner.files_found),
                    'hostname': scanner.scan_hostname
                })
                
                print(f"✅ Scan {i+1} completed: {len(scanner.files_found)} files found")
                
                # Small delay between scans
                time.sleep(1)
                
            finally:
                # Clean up config file
                if os.path.exists(config_file):
                    os.remove(config_file)
        
        # Show results summary
        print(f"\n" + "="*60)
        print("SCAN RESULTS SUMMARY")
        print("="*60)
        
        print(f"Total scans completed: {len(scan_results)}")
        print(f"All output files are unique - no conflicts!")
        
        print(f"\nGenerated files:")
        for i, result in enumerate(scan_results, 1):
            print(f"  Scan {i}:")
            print(f"    ID: {result['scan_id']}")
            print(f"    Output: {result['output_file']}")
            print(f"    Files: {result['files_found']}")
            print(f"    Exists: {'✅' if os.path.exists(result['output_file']) else '❌'}")
        
        # Check for duplicates report files
        print(f"\nDuplicates reports:")
        for i, result in enumerate(scan_results, 1):
            output_file = result['output_file']
            base_name = Path(output_file).stem
            extension = Path(output_file).suffix
            dup_file = f"{base_name}_duplicates{extension}"
            
            if os.path.exists(dup_file):
                print(f"  Scan {i}: {dup_file} ✅")
            else:
                print(f"  Scan {i}: No duplicates found")
        
        # Show file sizes
        print(f"\nFile information:")
        for i, result in enumerate(scan_results, 1):
            output_file = result['output_file']
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                print(f"  {output_file}: {size} bytes")
        
        return scan_results
        
    finally:
        # Clean up test directory
        shutil.rmtree(test_dir)
        print(f"\nCleaned up test directory: {test_dir}")


def demo_filename_conflicts():
    """Demonstrate how unique filenames prevent conflicts."""
    print("\n" + "="*60)
    print("CONFLICT PREVENTION DEMO")
    print("="*60)
    
    print("Without unique filenames:")
    print("  scan1: tutorial_inventory.csv")
    print("  scan2: tutorial_inventory.csv  ← OVERWRITES scan1!")
    print("  scan3: tutorial_inventory.csv  ← OVERWRITES scan2!")
    
    print("\nWith unique filenames:")
    for i in range(3):
        unique_name = generate_unique_filename("tutorial_inventory.csv")
        print(f"  scan{i+1}: {unique_name}")
        time.sleep(0.1)
    
    print("\n✅ All files are preserved - no data loss!")


def cleanup_demo_files():
    """Clean up any demo files created."""
    patterns = [
        "demo_scan_results_*.csv",
        "demo_scan_results_*_duplicates.csv",
        "tutorial_scanner.log"
    ]
    
    import glob
    cleaned_files = []
    
    for pattern in patterns:
        for file in glob.glob(pattern):
            try:
                os.remove(file)
                cleaned_files.append(file)
            except:
                pass
    
    if cleaned_files:
        print(f"\nCleaned up demo files:")
        for file in cleaned_files:
            print(f"  - {file}")


def main():
    """Run all demonstrations."""
    print("Tutorial Scanner - Unique Output Demonstration")
    print("=" * 60)
    
    try:
        # Demo 1: Unique filename generation
        demo_unique_filename_generation()
        
        # Demo 2: Multiple scans without conflicts
        scan_results = demo_multiple_scans()
        
        # Demo 3: Show conflict prevention
        demo_filename_conflicts()
        
        print(f"\n" + "="*60)
        print("DEMONSTRATION COMPLETE")
        print("="*60)
        
        print(f"\nKey benefits of unique output filenames:")
        print(f"✅ No file overwrites - all scan results preserved")
        print(f"✅ Easy identification with timestamps and scan IDs")
        print(f"✅ Hostname tracking for multi-machine environments")
        print(f"✅ Parallel scan support without conflicts")
        print(f"✅ Automatic cleanup and organization")
        
        print(f"\nUsage in production:")
        print(f"  python tutorial_scanner.py                    # Unique output enabled (default)")
        print(f"  python tutorial_scanner.py --no-unique-output # Disable unique output")
        print(f"  python tutorial_scanner.py --benchmark        # Show performance with unique files")
        
        return 0
        
    except Exception as e:
        print(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        # Clean up any remaining demo files
        cleanup_demo_files()


if __name__ == "__main__":
    exit(main())
