#!/usr/bin/env python3
"""
Example usage of the PDF scanner script.
This demonstrates how to use the pdf_scanner module programmatically.
"""

from pdf_scanner import find_pdf_files, write_to_csv
import os


def example_usage():
    """Example of how to use the PDF scanner functions."""
    
    # Example 1: Scan current directory
    print("Example 1: Scanning current directory")
    current_dir = "."
    pdf_files = find_pdf_files(current_dir)
    
    if pdf_files:
        write_to_csv(pdf_files, "current_dir_pdfs.csv")
    else:
        print("No PDF files found in current directory")
    
    print("\n" + "="*50 + "\n")
    
    # Example 2: Scan a specific directory (if it exists)
    test_dir = "/home/<USER>"  # Change this to a directory on your system
    if os.path.exists(test_dir):
        print(f"Example 2: Scanning {test_dir}")
        pdf_files = find_pdf_files(test_dir)
        
        if pdf_files:
            write_to_csv(pdf_files, "documents_pdfs.csv")
            
            # Print summary
            total_size = sum(int(f['size']) for f in pdf_files if f['size'] != -1)
            print(f"\nSummary:")
            print(f"Total PDF files found: {len(pdf_files)}")
            print(f"Total size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
        else:
            print("No PDF files found in Documents directory")
    else:
        print(f"Directory {test_dir} does not exist, skipping example 2")


if __name__ == "__main__":
    example_usage()
