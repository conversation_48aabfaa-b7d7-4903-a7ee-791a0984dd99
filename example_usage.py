#!/usr/bin/env python3
"""
Example usage of the PDF scanner script.
This demonstrates how to use the pdf_scanner module programmatically.
"""

from pdf_scanner import (
    find_pdf_files, write_to_csv, find_duplicates,
    generate_summary_report, write_duplicates_report, write_summary_report
)
import os


def example_usage():
    """Example of how to use the enhanced PDF scanner functions."""

    # Example 1: Basic scan with enhanced features
    print("Example 1: Enhanced scanning of current directory")
    current_dir = "."
    pdf_files, checksum_map = find_pdf_files(current_dir, 'sha256')

    if pdf_files:
        # Write main CSV
        write_to_csv(pdf_files, "enhanced_pdfs.csv")

        # Check for duplicates
        duplicates = find_duplicates(checksum_map)
        if duplicates:
            write_duplicates_report(duplicates, "duplicates_report.csv")
            print(f"Found {len(duplicates)} groups of duplicate files!")

        # Generate summary
        summary = generate_summary_report(pdf_files, duplicates)
        write_summary_report(summary, "scan_summary.json")

        # Print quick summary
        print(f"Total files: {summary['total_files']}")
        print(f"Total size: {summary['total_size_mb']} MB")
        print(f"Duplicates: {summary['duplicate_files']} files in {summary['duplicate_groups']} groups")

    else:
        print("No PDF files found in current directory")

    print("\n" + "="*50 + "\n")

    # Example 2: Detailed analysis of a directory
    test_dir = "/home/<USER>"  # Change this to a directory on your system
    if os.path.exists(test_dir):
        print(f"Example 2: Detailed analysis of {test_dir}")
        pdf_files, checksum_map = find_pdf_files(test_dir, 'md5')

        if pdf_files:
            # Generate all reports
            write_to_csv(pdf_files, "documents_analysis.csv")

            duplicates = find_duplicates(checksum_map)
            summary = generate_summary_report(pdf_files, duplicates)

            # Print detailed analysis
            print(f"\nDetailed Analysis:")
            print(f"Total PDF files: {summary['total_files']}")
            print(f"Unique files: {summary['unique_files']}")
            print(f"Duplicate files: {summary['duplicate_files']}")
            print(f"Total storage used: {summary['total_size_mb']} MB")
            print(f"Average file size: {summary['average_size_mb']} MB")

            if summary['largest_file']:
                largest = summary['largest_file']
                print(f"Largest file: {largest['filename']} ({largest['size_mb']} MB)")

            if summary['smallest_file']:
                smallest = summary['smallest_file']
                print(f"Smallest file: {smallest['filename']} ({smallest['size_mb']} MB)")

            # Show duplicate details
            if duplicates:
                print(f"\nDuplicate Groups:")
                for i, (checksum, paths) in enumerate(duplicates.items(), 1):
                    print(f"  Group {i} ({len(paths)} files):")
                    for path in paths:
                        print(f"    - {os.path.basename(path)}")
                    if i >= 3:  # Limit output for example
                        print(f"    ... and {len(duplicates) - 3} more groups")
                        break
        else:
            print("No PDF files found in Documents directory")
    else:
        print(f"Directory {test_dir} does not exist, skipping example 2")


if __name__ == "__main__":
    example_usage()
