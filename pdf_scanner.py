#!/usr/bin/env python3
"""
PDF Scanner Script

This script recursively scans a directory for PDF files and creates a CSV file
with information about each PDF including filename, full path, size, and checksum.
"""

import os
import csv
import hashlib
import argparse
from pathlib import Path
from typing import List, Dict, Set, Tuple
from datetime import datetime
from collections import defaultdict
import json


def calculate_checksum(file_path: str, algorithm: str = 'md5') -> str:
    """
    Calculate checksum for a file using the specified algorithm.
    
    Args:
        file_path (str): Path to the file
        algorithm (str): Hash algorithm to use ('md5', 'sha1', 'sha256')
    
    Returns:
        str: Hexadecimal checksum string
    """
    hash_obj = hashlib.new(algorithm)
    
    try:
        with open(file_path, 'rb') as f:
            # Read file in chunks to handle large files efficiently
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        return hash_obj.hexdigest()
    except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>) as e:
        print(f"Error reading file {file_path}: {e}")
        return "ERROR"


def get_file_info(file_path: str) -> Dict[str, any]:
    """
    Get comprehensive file information including size and timestamps.

    Args:
        file_path (str): Path to the file

    Returns:
        Dict[str, any]: Dictionary containing file information
    """
    try:
        stat_info = os.stat(file_path)
        return {
            'size': stat_info.st_size,
            'modified_time': datetime.fromtimestamp(stat_info.st_mtime),
            'created_time': datetime.fromtimestamp(stat_info.st_ctime),
            'accessed_time': datetime.fromtimestamp(stat_info.st_atime)
        }
    except (IOError, OSError) as e:
        print(f"Error getting file info for {file_path}: {e}")
        return {
            'size': -1,
            'modified_time': None,
            'created_time': None,
            'accessed_time': None
        }


def find_pdf_files(directory: str, hash_algorithm: str = 'md5') -> Tuple[List[Dict[str, any]], Dict[str, List[str]]]:
    """
    Recursively find all PDF files in the given directory with enhanced information.

    Args:
        directory (str): Root directory to search
        hash_algorithm (str): Hash algorithm to use for checksums

    Returns:
        Tuple[List[Dict[str, any]], Dict[str, List[str]]]:
            - List of dictionaries containing file information
            - Dictionary mapping checksums to lists of file paths (for duplicate detection)
    """
    pdf_files = []
    checksum_map = defaultdict(list)
    directory_path = Path(directory)

    if not directory_path.exists():
        print(f"Error: Directory '{directory}' does not exist.")
        return pdf_files, {}

    if not directory_path.is_dir():
        print(f"Error: '{directory}' is not a directory.")
        return pdf_files, {}

    print(f"Scanning directory: {directory}")

    # Use glob to find all PDF files recursively
    for pdf_file in directory_path.rglob("*.pdf"):
        if pdf_file.is_file():
            try:
                # Get comprehensive file information
                file_stats = get_file_info(str(pdf_file))
                checksum = calculate_checksum(str(pdf_file), hash_algorithm)

                file_info = {
                    'filename': pdf_file.name,
                    'path': str(pdf_file.absolute()),
                    'relative_path': str(pdf_file.relative_to(directory_path)),
                    'size': file_stats['size'],
                    'size_mb': round(file_stats['size'] / (1024 * 1024), 2) if file_stats['size'] > 0 else 0,
                    'modified_date': file_stats['modified_time'].strftime('%Y-%m-%d %H:%M:%S') if file_stats['modified_time'] else 'Unknown',
                    'created_date': file_stats['created_time'].strftime('%Y-%m-%d %H:%M:%S') if file_stats['created_time'] else 'Unknown',
                    'checksum': checksum,
                    'hash_algorithm': hash_algorithm
                }

                pdf_files.append(file_info)

                # Track checksums for duplicate detection
                if checksum != "ERROR":
                    checksum_map[checksum].append(str(pdf_file.absolute()))

                print(f"Found: {pdf_file.name} ({file_info['size_mb']} MB)")

            except Exception as e:
                print(f"Error processing {pdf_file}: {e}")

    return pdf_files, dict(checksum_map)


def find_duplicates(checksum_map: Dict[str, List[str]]) -> Dict[str, List[str]]:
    """
    Find duplicate files based on checksums.

    Args:
        checksum_map (Dict[str, List[str]]): Mapping of checksums to file paths

    Returns:
        Dict[str, List[str]]: Dictionary of checksums that have duplicates
    """
    duplicates = {}
    for checksum, file_paths in checksum_map.items():
        if len(file_paths) > 1:
            duplicates[checksum] = file_paths
    return duplicates


def generate_summary_report(pdf_files: List[Dict[str, any]], duplicates: Dict[str, List[str]]) -> Dict[str, any]:
    """
    Generate a summary report of the PDF scan.

    Args:
        pdf_files (List[Dict[str, any]]): List of PDF file information
        duplicates (Dict[str, List[str]]): Dictionary of duplicate files

    Returns:
        Dict[str, any]: Summary statistics
    """
    if not pdf_files:
        return {}

    total_files = len(pdf_files)
    total_size = sum(f['size'] for f in pdf_files if f['size'] > 0)
    total_size_mb = round(total_size / (1024 * 1024), 2)

    # Find largest and smallest files
    valid_files = [f for f in pdf_files if f['size'] > 0]
    largest_file = max(valid_files, key=lambda x: x['size']) if valid_files else None
    smallest_file = min(valid_files, key=lambda x: x['size']) if valid_files else None

    # Count duplicates
    duplicate_files = sum(len(paths) for paths in duplicates.values())
    unique_files = total_files - duplicate_files + len(duplicates)

    return {
        'total_files': total_files,
        'unique_files': unique_files,
        'duplicate_files': duplicate_files,
        'duplicate_groups': len(duplicates),
        'total_size_bytes': total_size,
        'total_size_mb': total_size_mb,
        'average_size_mb': round(total_size_mb / total_files, 2) if total_files > 0 else 0,
        'largest_file': largest_file,
        'smallest_file': smallest_file
    }


def write_to_csv(pdf_files: List[Dict[str, any]], output_file: str) -> None:
    """
    Write PDF file information to a CSV file.

    Args:
        pdf_files (List[Dict[str, any]]): List of PDF file information
        output_file (str): Output CSV file path
    """
    if not pdf_files:
        print("No PDF files found to write to CSV.")
        return

    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'filename', 'path', 'relative_path', 'size', 'size_mb',
                'modified_date', 'created_date', 'checksum', 'hash_algorithm'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # Write header
            writer.writeheader()

            # Write data
            for file_info in pdf_files:
                writer.writerow(file_info)

        print(f"CSV file created successfully: {output_file}")
        print(f"Total PDF files processed: {len(pdf_files)}")

    except (IOError, OSError) as e:
        print(f"Error writing to CSV file: {e}")


def write_duplicates_report(duplicates: Dict[str, List[str]], output_file: str) -> None:
    """
    Write duplicate files report to a CSV file.

    Args:
        duplicates (Dict[str, List[str]]): Dictionary of duplicate files
        output_file (str): Output CSV file path
    """
    if not duplicates:
        print("No duplicate files found.")
        return

    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['checksum', 'duplicate_count', 'file_paths']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # Write header
            writer.writeheader()

            # Write data
            for checksum, file_paths in duplicates.items():
                writer.writerow({
                    'checksum': checksum,
                    'duplicate_count': len(file_paths),
                    'file_paths': ' | '.join(file_paths)
                })

        print(f"Duplicates report created: {output_file}")
        print(f"Found {len(duplicates)} groups of duplicate files")

    except (IOError, OSError) as e:
        print(f"Error writing duplicates report: {e}")


def write_summary_report(summary: Dict[str, any], output_file: str) -> None:
    """
    Write summary report to a JSON file.

    Args:
        summary (Dict[str, any]): Summary statistics
        output_file (str): Output JSON file path
    """
    if not summary:
        print("No summary data to write.")
        return

    try:
        with open(output_file, 'w', encoding='utf-8') as jsonfile:
            json.dump(summary, jsonfile, indent=2, default=str)

        print(f"Summary report created: {output_file}")

    except (IOError, OSError) as e:
        print(f"Error writing summary report: {e}")


def main():
    """Main function to handle command line arguments and execute the script."""
    parser = argparse.ArgumentParser(
        description="Recursively scan directory for PDF files and create comprehensive reports"
    )
    parser.add_argument(
        "directory",
        help="Directory to scan for PDF files"
    )
    parser.add_argument(
        "-o", "--output",
        default="pdf_files.csv",
        help="Output CSV file name (default: pdf_files.csv)"
    )
    parser.add_argument(
        "--hash",
        choices=['md5', 'sha1', 'sha256'],
        default='md5',
        help="Hash algorithm for checksum (default: md5)"
    )
    parser.add_argument(
        "--duplicates",
        action="store_true",
        help="Generate duplicate files report"
    )
    parser.add_argument(
        "--summary",
        action="store_true",
        help="Generate summary statistics report"
    )
    parser.add_argument(
        "--all-reports",
        action="store_true",
        help="Generate all reports (main CSV, duplicates, and summary)"
    )

    args = parser.parse_args()

    # Validate input directory
    if not os.path.exists(args.directory):
        print(f"Error: Directory '{args.directory}' does not exist.")
        return 1

    # Find PDF files with enhanced information
    print(f"Scanning for PDF files using {args.hash} checksums...")
    pdf_files, checksum_map = find_pdf_files(args.directory, args.hash)

    if not pdf_files:
        print("No PDF files found in the specified directory.")
        return 0

    # Write main CSV report
    write_to_csv(pdf_files, args.output)

    # Generate additional reports if requested
    if args.duplicates or args.all_reports:
        duplicates = find_duplicates(checksum_map)
        if duplicates:
            duplicates_file = args.output.replace('.csv', '_duplicates.csv')
            write_duplicates_report(duplicates, duplicates_file)
        else:
            print("No duplicate files found.")

    if args.summary or args.all_reports:
        duplicates = find_duplicates(checksum_map)
        summary = generate_summary_report(pdf_files, duplicates)
        summary_file = args.output.replace('.csv', '_summary.json')
        write_summary_report(summary, summary_file)

        # Print summary to console
        print("\n" + "="*50)
        print("SCAN SUMMARY")
        print("="*50)
        print(f"Total PDF files found: {summary['total_files']}")
        print(f"Unique files: {summary['unique_files']}")
        print(f"Duplicate files: {summary['duplicate_files']}")
        print(f"Duplicate groups: {summary['duplicate_groups']}")
        print(f"Total size: {summary['total_size_mb']} MB")
        print(f"Average file size: {summary['average_size_mb']} MB")

        if summary['largest_file']:
            print(f"Largest file: {summary['largest_file']['filename']} ({summary['largest_file']['size_mb']} MB)")
        if summary['smallest_file']:
            print(f"Smallest file: {summary['smallest_file']['filename']} ({summary['smallest_file']['size_mb']} MB)")

    return 0


if __name__ == "__main__":
    exit(main())
