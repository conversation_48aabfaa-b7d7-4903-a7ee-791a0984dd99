#!/usr/bin/env python3
"""
Multicore PDF Scanner Script

This is a high-performance version of the PDF scanner that uses multiprocessing
to scan PDF files in parallel, significantly improving performance for large directories.
"""

import os
import csv
import hashlib
import argparse
from pathlib import Path
from typing import List, Dict, Set, Tuple, Optional
from datetime import datetime
from collections import defaultdict
import json
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import time
from functools import partial


def calculate_checksum(file_path: str, algorithm: str = 'md5') -> str:
    """
    Calculate checksum for a file using the specified algorithm.
    
    Args:
        file_path (str): Path to the file
        algorithm (str): Hash algorithm to use ('md5', 'sha1', 'sha256')
    
    Returns:
        str: Hexadecimal checksum string
    """
    hash_obj = hashlib.new(algorithm)
    
    try:
        with open(file_path, 'rb') as f:
            # Read file in chunks to handle large files efficiently
            for chunk in iter(lambda: f.read(8192), b""):  # Increased chunk size
                hash_obj.update(chunk)
        return hash_obj.hexdigest()
    except (<PERSON><PERSON>rro<PERSON>, OSError) as e:
        print(f"Error reading file {file_path}: {e}")
        return "ERROR"


def get_file_info(file_path: str) -> Dict[str, any]:
    """
    Get comprehensive file information including size and timestamps.
    
    Args:
        file_path (str): Path to the file
    
    Returns:
        Dict[str, any]: Dictionary containing file information
    """
    try:
        stat_info = os.stat(file_path)
        return {
            'size': stat_info.st_size,
            'modified_time': datetime.fromtimestamp(stat_info.st_mtime),
            'created_time': datetime.fromtimestamp(stat_info.st_ctime),
            'accessed_time': datetime.fromtimestamp(stat_info.st_atime)
        }
    except (IOError, OSError) as e:
        print(f"Error getting file info for {file_path}: {e}")
        return {
            'size': -1,
            'modified_time': None,
            'created_time': None,
            'accessed_time': None
        }


def process_single_pdf(args: Tuple[str, str, str]) -> Optional[Dict[str, any]]:
    """
    Process a single PDF file - designed to be called by multiprocessing.
    
    Args:
        args: Tuple of (file_path, base_directory, hash_algorithm)
    
    Returns:
        Optional[Dict[str, any]]: File information dictionary or None if error
    """
    file_path, base_directory, hash_algorithm = args
    
    try:
        pdf_file = Path(file_path)
        base_path = Path(base_directory)
        
        # Get comprehensive file information
        file_stats = get_file_info(str(pdf_file))
        checksum = calculate_checksum(str(pdf_file), hash_algorithm)
        
        file_info = {
            'filename': pdf_file.name,
            'path': str(pdf_file.absolute()),
            'relative_path': str(pdf_file.relative_to(base_path)),
            'size': file_stats['size'],
            'size_mb': round(file_stats['size'] / (1024 * 1024), 2) if file_stats['size'] > 0 else 0,
            'modified_date': file_stats['modified_time'].strftime('%Y-%m-%d %H:%M:%S') if file_stats['modified_time'] else 'Unknown',
            'created_date': file_stats['created_time'].strftime('%Y-%m-%d %H:%M:%S') if file_stats['created_time'] else 'Unknown',
            'checksum': checksum,
            'hash_algorithm': hash_algorithm
        }
        
        return file_info
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return None


def find_pdf_files_parallel(directory: str, hash_algorithm: str = 'md5', max_workers: Optional[int] = None) -> Tuple[List[Dict[str, any]], Dict[str, List[str]]]:
    """
    Recursively find all PDF files in the given directory using parallel processing.
    
    Args:
        directory (str): Root directory to search
        hash_algorithm (str): Hash algorithm to use for checksums
        max_workers (Optional[int]): Maximum number of worker processes (default: CPU count)
    
    Returns:
        Tuple[List[Dict[str, any]], Dict[str, List[str]]]: 
            - List of dictionaries containing file information
            - Dictionary mapping checksums to lists of file paths (for duplicate detection)
    """
    pdf_files = []
    checksum_map = defaultdict(list)
    directory_path = Path(directory)
    
    if not directory_path.exists():
        print(f"Error: Directory '{directory}' does not exist.")
        return pdf_files, {}
    
    if not directory_path.is_dir():
        print(f"Error: '{directory}' is not a directory.")
        return pdf_files, {}
    
    print(f"Scanning directory: {directory}")
    
    # Find all PDF files first
    pdf_file_paths = []
    for pdf_file in directory_path.rglob("*.pdf"):
        if pdf_file.is_file():
            pdf_file_paths.append(str(pdf_file))
    
    if not pdf_file_paths:
        print("No PDF files found.")
        return pdf_files, {}
    
    print(f"Found {len(pdf_file_paths)} PDF files. Processing with parallel workers...")
    
    # Determine number of workers
    if max_workers is None:
        max_workers = min(mp.cpu_count(), len(pdf_file_paths))
    
    print(f"Using {max_workers} worker processes")
    
    # Prepare arguments for parallel processing
    process_args = [(file_path, directory, hash_algorithm) for file_path in pdf_file_paths]
    
    # Process files in parallel
    start_time = time.time()
    processed_count = 0
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_path = {executor.submit(process_single_pdf, args): args[0] for args in process_args}
        
        # Process completed tasks
        for future in as_completed(future_to_path):
            file_path = future_to_path[future]
            try:
                result = future.result()
                if result:
                    pdf_files.append(result)
                    
                    # Track checksums for duplicate detection
                    if result['checksum'] != "ERROR":
                        checksum_map[result['checksum']].append(result['path'])
                    
                    processed_count += 1
                    
                    # Progress indicator
                    if processed_count % 10 == 0 or processed_count == len(pdf_file_paths):
                        elapsed = time.time() - start_time
                        rate = processed_count / elapsed if elapsed > 0 else 0
                        print(f"Processed {processed_count}/{len(pdf_file_paths)} files ({rate:.1f} files/sec)")
                        
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
    
    elapsed_time = time.time() - start_time
    print(f"Completed processing {len(pdf_files)} files in {elapsed_time:.2f} seconds")
    print(f"Average processing rate: {len(pdf_files)/elapsed_time:.1f} files/second")
    
    return pdf_files, dict(checksum_map)


def find_duplicates(checksum_map: Dict[str, List[str]]) -> Dict[str, List[str]]:
    """
    Find duplicate files based on checksums.
    
    Args:
        checksum_map (Dict[str, List[str]]): Mapping of checksums to file paths
    
    Returns:
        Dict[str, List[str]]: Dictionary of checksums that have duplicates
    """
    duplicates = {}
    for checksum, file_paths in checksum_map.items():
        if len(file_paths) > 1:
            duplicates[checksum] = file_paths
    return duplicates


def generate_summary_report(pdf_files: List[Dict[str, any]], duplicates: Dict[str, List[str]]) -> Dict[str, any]:
    """
    Generate a summary report of the PDF scan.
    
    Args:
        pdf_files (List[Dict[str, any]]): List of PDF file information
        duplicates (Dict[str, List[str]]): Dictionary of duplicate files
    
    Returns:
        Dict[str, any]: Summary statistics
    """
    if not pdf_files:
        return {}
    
    total_files = len(pdf_files)
    total_size = sum(f['size'] for f in pdf_files if f['size'] > 0)
    total_size_mb = round(total_size / (1024 * 1024), 2)
    
    # Find largest and smallest files
    valid_files = [f for f in pdf_files if f['size'] > 0]
    largest_file = max(valid_files, key=lambda x: x['size']) if valid_files else None
    smallest_file = min(valid_files, key=lambda x: x['size']) if valid_files else None
    
    # Count duplicates
    duplicate_files = sum(len(paths) for paths in duplicates.values())
    unique_files = total_files - duplicate_files + len(duplicates)
    
    return {
        'total_files': total_files,
        'unique_files': unique_files,
        'duplicate_files': duplicate_files,
        'duplicate_groups': len(duplicates),
        'total_size_bytes': total_size,
        'total_size_mb': total_size_mb,
        'average_size_mb': round(total_size_mb / total_files, 2) if total_files > 0 else 0,
        'largest_file': largest_file,
        'smallest_file': smallest_file
    }


def write_to_csv(pdf_files: List[Dict[str, any]], output_file: str, separator: str = '|~|') -> None:
    """
    Write PDF file information to a CSV file with custom separator.

    Args:
        pdf_files (List[Dict[str, any]]): List of PDF file information
        output_file (str): Output CSV file path
        separator (str): Custom separator to use (default: '|~|')
    """
    if not pdf_files:
        print("No PDF files found to write to CSV.")
        return

    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'filename', 'path', 'relative_path', 'size', 'size_mb',
                'modified_date', 'created_date', 'checksum', 'hash_algorithm'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames, delimiter=separator)

            # Write header
            writer.writeheader()

            # Write data
            for file_info in pdf_files:
                writer.writerow(file_info)

        print(f"CSV file created successfully: {output_file}")
        print(f"Using separator: '{separator}'")
        print(f"Total PDF files processed: {len(pdf_files)}")

    except (IOError, OSError) as e:
        print(f"Error writing to CSV file: {e}")


def write_duplicates_report(duplicates: Dict[str, List[str]], output_file: str, separator: str = '|~|') -> None:
    """
    Write duplicate files report to a CSV file with custom separator.

    Args:
        duplicates (Dict[str, List[str]]): Dictionary of duplicate files
        output_file (str): Output CSV file path
        separator (str): Custom separator to use (default: '|~|')
    """
    if not duplicates:
        print("No duplicate files found.")
        return

    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['checksum', 'duplicate_count', 'file_paths']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames, delimiter=separator)

            # Write header
            writer.writeheader()

            # Write data
            for checksum, file_paths in duplicates.items():
                # Use a different separator for file paths within the field to avoid conflicts
                writer.writerow({
                    'checksum': checksum,
                    'duplicate_count': len(file_paths),
                    'file_paths': ' ¦¦¦ '.join(file_paths)  # Using triple pipe separator for file paths
                })

        print(f"Duplicates report created: {output_file}")
        print(f"Using separator: '{separator}'")
        print(f"File paths separated by: ' ¦¦¦ '")
        print(f"Found {len(duplicates)} groups of duplicate files")

    except (IOError, OSError) as e:
        print(f"Error writing duplicates report: {e}")


def write_summary_report(summary: Dict[str, any], output_file: str) -> None:
    """
    Write summary report to a JSON file.
    
    Args:
        summary (Dict[str, any]): Summary statistics
        output_file (str): Output JSON file path
    """
    if not summary:
        print("No summary data to write.")
        return
    
    try:
        with open(output_file, 'w', encoding='utf-8') as jsonfile:
            json.dump(summary, jsonfile, indent=2, default=str)
        
        print(f"Summary report created: {output_file}")
        
    except (IOError, OSError) as e:
        print(f"Error writing summary report: {e}")


def main():
    """Main function to handle command line arguments and execute the script."""
    parser = argparse.ArgumentParser(
        description="High-performance multicore PDF scanner with parallel processing"
    )
    parser.add_argument(
        "directory",
        help="Directory to scan for PDF files"
    )
    parser.add_argument(
        "-o", "--output",
        default="pdf_files_multicore.csv",
        help="Output CSV file name (default: pdf_files_multicore.csv)"
    )
    parser.add_argument(
        "--hash",
        choices=['md5', 'sha1', 'sha256'],
        default='md5',
        help="Hash algorithm for checksum (default: md5)"
    )
    parser.add_argument(
        "--duplicates",
        action="store_true",
        help="Generate duplicate files report"
    )
    parser.add_argument(
        "--summary",
        action="store_true",
        help="Generate summary statistics report"
    )
    parser.add_argument(
        "--all-reports",
        action="store_true",
        help="Generate all reports (main CSV, duplicates, and summary)"
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=None,
        help="Number of worker processes (default: auto-detect based on CPU cores)"
    )
    parser.add_argument(
        "--benchmark",
        action="store_true",
        help="Show detailed performance benchmarks"
    )
    parser.add_argument(
        "--separator",
        default="|~|",
        help="Custom separator for CSV files (default: |~|)"
    )

    args = parser.parse_args()

    # Validate input directory
    if not os.path.exists(args.directory):
        print(f"Error: Directory '{args.directory}' does not exist.")
        return 1

    # Show system information
    cpu_count = mp.cpu_count()
    workers = args.workers if args.workers else cpu_count
    print(f"System: {cpu_count} CPU cores detected")
    print(f"Using: {workers} worker processes")

    if args.benchmark:
        print(f"Hash algorithm: {args.hash}")
        print(f"Target directory: {args.directory}")
        print("-" * 50)

    # Find PDF files with enhanced information and parallel processing
    start_time = time.time()
    print(f"Starting multicore scan using {args.hash} checksums...")

    pdf_files, checksum_map = find_pdf_files_parallel(
        args.directory,
        args.hash,
        max_workers=workers
    )

    scan_time = time.time() - start_time

    if not pdf_files:
        print("No PDF files found in the specified directory.")
        return 0

    # Write main CSV report
    write_start = time.time()
    write_to_csv(pdf_files, args.output, args.separator)
    write_time = time.time() - write_start

    # Generate additional reports if requested
    duplicates = {}
    if args.duplicates or args.all_reports:
        duplicates = find_duplicates(checksum_map)
        if duplicates:
            duplicates_file = args.output.replace('.csv', '_duplicates.csv')
            write_duplicates_report(duplicates, duplicates_file, args.separator)
        else:
            print("No duplicate files found.")

    if args.summary or args.all_reports:
        if not duplicates:
            duplicates = find_duplicates(checksum_map)

        summary = generate_summary_report(pdf_files, duplicates)
        summary_file = args.output.replace('.csv', '_summary.json')
        write_summary_report(summary, summary_file)

        # Print summary to console
        print("\n" + "="*50)
        print("SCAN SUMMARY")
        print("="*50)
        print(f"Total PDF files found: {summary['total_files']}")
        print(f"Unique files: {summary['unique_files']}")
        print(f"Duplicate files: {summary['duplicate_files']}")
        print(f"Duplicate groups: {summary['duplicate_groups']}")
        print(f"Total size: {summary['total_size_mb']} MB")
        print(f"Average file size: {summary['average_size_mb']} MB")

        if summary['largest_file']:
            print(f"Largest file: {summary['largest_file']['filename']} ({summary['largest_file']['size_mb']} MB)")
        if summary['smallest_file']:
            print(f"Smallest file: {summary['smallest_file']['filename']} ({summary['smallest_file']['size_mb']} MB)")

    # Performance benchmarks
    if args.benchmark:
        total_time = time.time() - start_time
        total_size_mb = sum(f['size'] for f in pdf_files) / (1024 * 1024)

        print("\n" + "="*50)
        print("PERFORMANCE BENCHMARKS")
        print("="*50)
        print(f"Total execution time: {total_time:.2f} seconds")
        print(f"File scanning time: {scan_time:.2f} seconds")
        print(f"CSV writing time: {write_time:.2f} seconds")
        print(f"Files processed: {len(pdf_files)}")
        print(f"Processing rate: {len(pdf_files)/scan_time:.1f} files/second")
        print(f"Data processed: {total_size_mb:.1f} MB")
        print(f"Throughput: {total_size_mb/scan_time:.1f} MB/second")
        print(f"Workers used: {workers}")
        print(f"Efficiency: {len(pdf_files)/(scan_time*workers):.1f} files/second/worker")

    return 0


if __name__ == "__main__":
    # Ensure proper multiprocessing behavior on all platforms
    mp.set_start_method('spawn', force=True)
    exit(main())
