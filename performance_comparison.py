#!/usr/bin/env python3
"""
Performance comparison script between single-core and multicore PDF scanners.
"""

import time
import tempfile
import shutil
import os
from pathlib import Path
import multiprocessing as mp

# Import both versions
from pdf_scanner import find_pdf_files as find_pdf_files_single
from pdf_scanner_multicore import find_pdf_files_parallel


def create_test_pdfs(num_files: int = 50, base_dir: str = None) -> str:
    """
    Create a test directory with multiple PDF files for performance testing.
    
    Args:
        num_files (int): Number of test PDF files to create
        base_dir (str): Base directory (if None, creates temp directory)
    
    Returns:
        str: Path to the test directory
    """
    if base_dir is None:
        test_dir = tempfile.mkdtemp(prefix="pdf_perf_test_")
    else:
        test_dir = base_dir
        os.makedirs(test_dir, exist_ok=True)
    
    print(f"Creating {num_files} test PDF files in {test_dir}")
    
    # Create subdirectories for more realistic structure
    subdirs = [
        Path(test_dir) / "documents",
        Path(test_dir) / "archive" / "2023",
        Path(test_dir) / "archive" / "2022", 
        Path(test_dir) / "reports" / "monthly",
        Path(test_dir) / "reports" / "yearly",
        Path(test_dir) / "temp"
    ]
    
    for subdir in subdirs:
        subdir.mkdir(parents=True, exist_ok=True)
    
    # Create test content of varying sizes
    test_contents = [
        "Small PDF content " * 100,  # ~1.8KB
        "Medium PDF content " * 1000,  # ~18KB  
        "Large PDF content " * 5000,  # ~90KB
        "Extra large PDF content " * 10000,  # ~180KB
    ]
    
    # Create files distributed across subdirectories
    for i in range(num_files):
        # Choose directory and content size
        target_dir = subdirs[i % len(subdirs)]
        content = test_contents[i % len(test_contents)]
        
        # Add some variation to make files unique
        content += f"\nFile number: {i}\nTimestamp: {time.time()}\n" + "X" * (i * 10)
        
        file_path = target_dir / f"test_document_{i:03d}.pdf"
        
        with open(file_path, 'w') as f:
            f.write(content)
    
    print(f"Created {num_files} test files")
    return test_dir


def benchmark_single_core(test_dir: str, hash_algo: str = 'md5') -> dict:
    """Benchmark the single-core version."""
    print(f"\n{'='*50}")
    print("SINGLE-CORE BENCHMARK")
    print(f"{'='*50}")
    
    start_time = time.time()
    pdf_files, checksum_map = find_pdf_files_single(test_dir, hash_algo)
    end_time = time.time()
    
    elapsed = end_time - start_time
    
    results = {
        'version': 'Single-core',
        'files_found': len(pdf_files),
        'time_seconds': elapsed,
        'files_per_second': len(pdf_files) / elapsed if elapsed > 0 else 0,
        'hash_algorithm': hash_algo
    }
    
    print(f"Files processed: {results['files_found']}")
    print(f"Time taken: {results['time_seconds']:.2f} seconds")
    print(f"Processing rate: {results['files_per_second']:.1f} files/second")
    
    return results


def benchmark_multicore(test_dir: str, hash_algo: str = 'md5', workers: int = None) -> dict:
    """Benchmark the multicore version."""
    if workers is None:
        workers = mp.cpu_count()
    
    print(f"\n{'='*50}")
    print(f"MULTICORE BENCHMARK ({workers} workers)")
    print(f"{'='*50}")
    
    start_time = time.time()
    pdf_files, checksum_map = find_pdf_files_parallel(test_dir, hash_algo, workers)
    end_time = time.time()
    
    elapsed = end_time - start_time
    
    results = {
        'version': f'Multicore ({workers} workers)',
        'files_found': len(pdf_files),
        'time_seconds': elapsed,
        'files_per_second': len(pdf_files) / elapsed if elapsed > 0 else 0,
        'workers': workers,
        'hash_algorithm': hash_algo,
        'efficiency': (len(pdf_files) / elapsed / workers) if elapsed > 0 else 0
    }
    
    print(f"Files processed: {results['files_found']}")
    print(f"Time taken: {results['time_seconds']:.2f} seconds")
    print(f"Processing rate: {results['files_per_second']:.1f} files/second")
    print(f"Efficiency: {results['efficiency']:.1f} files/second/worker")
    
    return results


def run_comprehensive_benchmark():
    """Run a comprehensive performance comparison."""
    print("PDF Scanner Performance Comparison")
    print("=" * 60)
    
    # System info
    cpu_count = mp.cpu_count()
    print(f"System: {cpu_count} CPU cores detected")
    
    # Test configurations
    test_configs = [
        {'files': 20, 'hash': 'md5'},
        {'files': 50, 'hash': 'md5'},
        {'files': 100, 'hash': 'md5'},
        {'files': 50, 'hash': 'sha256'},  # More CPU-intensive
    ]
    
    all_results = []
    
    for config in test_configs:
        print(f"\n{'#'*60}")
        print(f"TEST: {config['files']} files with {config['hash']} hashing")
        print(f"{'#'*60}")
        
        # Create test files
        test_dir = create_test_pdfs(config['files'])
        
        try:
            # Benchmark single-core
            single_results = benchmark_single_core(test_dir, config['hash'])
            
            # Benchmark multicore with different worker counts
            worker_counts = [2, 4, cpu_count] if cpu_count >= 4 else [2, cpu_count]
            
            multicore_results = []
            for workers in worker_counts:
                if workers <= cpu_count:
                    result = benchmark_multicore(test_dir, config['hash'], workers)
                    multicore_results.append(result)
            
            # Calculate speedup
            best_multicore = max(multicore_results, key=lambda x: x['files_per_second'])
            speedup = best_multicore['files_per_second'] / single_results['files_per_second']
            
            print(f"\n{'='*50}")
            print("COMPARISON SUMMARY")
            print(f"{'='*50}")
            print(f"Single-core: {single_results['files_per_second']:.1f} files/sec")
            print(f"Best multicore: {best_multicore['files_per_second']:.1f} files/sec ({best_multicore['workers']} workers)")
            print(f"Speedup: {speedup:.1f}x faster")
            print(f"Time saved: {single_results['time_seconds'] - best_multicore['time_seconds']:.1f} seconds")
            
            # Store results
            test_result = {
                'config': config,
                'single_core': single_results,
                'multicore_results': multicore_results,
                'best_multicore': best_multicore,
                'speedup': speedup
            }
            all_results.append(test_result)
            
        finally:
            # Cleanup
            shutil.rmtree(test_dir)
            print(f"Cleaned up test directory: {test_dir}")
    
    # Overall summary
    print(f"\n{'#'*60}")
    print("OVERALL PERFORMANCE SUMMARY")
    print(f"{'#'*60}")
    
    for i, result in enumerate(all_results, 1):
        config = result['config']
        speedup = result['speedup']
        print(f"Test {i}: {config['files']} files, {config['hash']} - {speedup:.1f}x speedup")
    
    avg_speedup = sum(r['speedup'] for r in all_results) / len(all_results)
    print(f"\nAverage speedup: {avg_speedup:.1f}x")
    
    print(f"\nRecommendation:")
    if avg_speedup > 2:
        print("✅ Multicore version provides significant performance improvement!")
        print("   Use pdf_scanner_multicore.py for large directories.")
    elif avg_speedup > 1.5:
        print("✅ Multicore version provides moderate performance improvement.")
        print("   Recommended for directories with 50+ PDF files.")
    else:
        print("ℹ️  Multicore version provides minimal improvement for small datasets.")
        print("   Single-core version may be sufficient for small directories.")


def quick_test():
    """Run a quick test with a small number of files."""
    print("Quick Performance Test")
    print("=" * 40)
    
    # Create small test
    test_dir = create_test_pdfs(10)
    
    try:
        print("\nTesting single-core version...")
        single_start = time.time()
        single_files, _ = find_pdf_files_single(test_dir)
        single_time = time.time() - single_start
        
        print("\nTesting multicore version...")
        multi_start = time.time()
        multi_files, _ = find_pdf_files_parallel(test_dir)
        multi_time = time.time() - multi_start
        
        print(f"\nResults:")
        print(f"Single-core: {len(single_files)} files in {single_time:.2f}s")
        print(f"Multicore:   {len(multi_files)} files in {multi_time:.2f}s")
        
        if multi_time > 0:
            speedup = single_time / multi_time
            print(f"Speedup: {speedup:.1f}x")
        
    finally:
        shutil.rmtree(test_dir)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        quick_test()
    else:
        run_comprehensive_benchmark()
