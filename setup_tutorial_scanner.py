#!/usr/bin/env python3
"""
Setup script for Tutorial Scanner

This script helps set up the tutorial scanner with proper dependencies
and configuration for your environment.
"""

import subprocess
import sys
import os
import json
from pathlib import Path


def install_dependencies():
    """Install required Python packages."""
    packages = [
        'paramiko',  # For SSH connections
        'tqdm',      # For progress bars
    ]
    
    print("Installing required dependencies...")
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            print(f"You can install manually with: pip install {package}")


def create_ssh_key_guide():
    """Create a guide for SSH key setup."""
    guide_content = """
# SSH Key Setup Guide for Tutorial Scanner

## 1. Generate SSH Key Pair (if you don't have one)

### On Linux/macOS:
```bash
# Generate a new SSH key
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Default location: ~/.ssh/id_rsa (private) and ~/.ssh/id_rsa.pub (public)
```

### On Windows:
```cmd
# Using Git Bash or WSL
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Or use PuTTY Key Generator (puttygen.exe)
```

## 2. Copy Public Key to Remote Servers

### Method 1: Using ssh-copy-id (Linux/macOS)
```bash
ssh-copy-id username@server_hostname
```

### Method 2: Manual copy
```bash
# Copy the public key content
cat ~/.ssh/id_rsa.pub

# Then on the remote server, add it to:
# ~/.ssh/authorized_keys
```

### Method 3: Using SCP
```bash
scp ~/.ssh/id_rsa.pub username@server:/tmp/
ssh username@server
cat /tmp/id_rsa.pub >> ~/.ssh/authorized_keys
rm /tmp/id_rsa.pub
```

## 3. Test SSH Connection
```bash
ssh -i ~/.ssh/id_rsa username@server_hostname
```

## 4. SSH Config File (Optional but Recommended)

Create/edit `~/.ssh/config`:
```
Host tutorial-server1
    HostName server1.example.com
    User your_username
    IdentityFile ~/.ssh/id_rsa
    Port 22

Host tutorial-server2
    HostName *************
    User admin
    IdentityFile ~/.ssh/id_rsa
```

Then you can connect with: `ssh tutorial-server1`

## 5. Security Best Practices

1. **Use strong passphrases** for your private keys
2. **Restrict private key permissions**: `chmod 600 ~/.ssh/id_rsa`
3. **Use different keys** for different purposes
4. **Disable password authentication** on servers once key auth is working
5. **Use SSH agent** to avoid typing passphrases repeatedly:
   ```bash
   ssh-add ~/.ssh/id_rsa
   ```

## 6. Troubleshooting

### Permission Issues:
```bash
chmod 700 ~/.ssh
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub
chmod 600 ~/.ssh/authorized_keys  # on remote server
```

### Debug SSH Connection:
```bash
ssh -v username@server_hostname
```

### Test SFTP Access:
```bash
sftp username@server_hostname
```
"""
    
    try:
        with open('SSH_SETUP_GUIDE.md', 'w') as f:
            f.write(guide_content)
        print("✅ SSH setup guide created: SSH_SETUP_GUIDE.md")
    except Exception as e:
        print(f"❌ Error creating SSH guide: {e}")


def create_interactive_config():
    """Create configuration file interactively."""
    print("\n" + "="*60)
    print("INTERACTIVE CONFIGURATION SETUP")
    print("="*60)
    
    config = {
        "ssh_servers": [],
        "local_directories": [],
        "file_extensions": [
            ".pdf", ".mp4", ".mkv", ".avi", ".mov", ".wmv",
            ".txt", ".md", ".rst", ".docx", ".pptx", ".epub",
            ".zip", ".rar", ".7z"
        ],
        "output_file": "tutorial_inventory.csv",
        "separator": "|~|",
        "hash_algorithm": "md5",
        "max_file_size_mb": 10000,
        "ssh_timeout": 30,
        "chunk_size": 8192,
        "log_level": "INFO"
    }
    
    # SSH Servers
    print("\n1. SSH Server Configuration")
    print("-" * 30)
    
    while True:
        add_ssh = input("Add an SSH server? (y/n): ").lower().strip()
        if add_ssh != 'y':
            break
        
        server = {}
        server['hostname'] = input("  Hostname/IP: ").strip()
        server['username'] = input("  Username: ").strip()
        
        auth_method = input("  Authentication method (key/password): ").lower().strip()
        if auth_method == 'key':
            default_key = str(Path.home() / '.ssh' / 'id_rsa')
            key_file = input(f"  Private key file [{default_key}]: ").strip()
            server['key_file'] = key_file if key_file else default_key
        else:
            server['password'] = input("  Password: ").strip()
        
        port = input("  Port [22]: ").strip()
        server['port'] = int(port) if port else 22
        
        print("  Directories to scan (one per line, empty line to finish):")
        directories = []
        while True:
            directory = input("    Directory: ").strip()
            if not directory:
                break
            directories.append(directory)
        server['directories'] = directories if directories else ['/']
        
        config['ssh_servers'].append(server)
        print(f"  ✅ Added server: {server['hostname']}")
    
    # Local Directories
    print("\n2. Local Directory Configuration")
    print("-" * 35)
    
    print("Local directories to scan (one per line, empty line to finish):")
    while True:
        directory = input("  Directory: ").strip()
        if not directory:
            break
        if os.path.exists(directory):
            config['local_directories'].append(directory)
            print(f"  ✅ Added: {directory}")
        else:
            print(f"  ⚠️  Directory not found: {directory}")
            add_anyway = input("    Add anyway? (y/n): ").lower().strip()
            if add_anyway == 'y':
                config['local_directories'].append(directory)
    
    if not config['local_directories']:
        config['local_directories'] = ['.']  # Current directory as default
    
    # File Extensions
    print("\n3. File Extensions")
    print("-" * 20)
    print("Current extensions:", ', '.join(config['file_extensions']))
    
    modify_ext = input("Modify file extensions? (y/n): ").lower().strip()
    if modify_ext == 'y':
        print("Enter extensions (one per line, empty line to finish):")
        extensions = []
        while True:
            ext = input("  Extension (e.g., .pdf): ").strip()
            if not ext:
                break
            if not ext.startswith('.'):
                ext = '.' + ext
            extensions.append(ext.lower())
        
        if extensions:
            config['file_extensions'] = extensions
    
    # Output Configuration
    print("\n4. Output Configuration")
    print("-" * 25)
    
    output_file = input(f"Output file [{config['output_file']}]: ").strip()
    if output_file:
        config['output_file'] = output_file
    
    separator = input(f"CSV separator [{config['separator']}]: ").strip()
    if separator:
        config['separator'] = separator
    
    # Save configuration
    config_file = "tutorial_scanner_config.json"
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"\n✅ Configuration saved to: {config_file}")
        return config_file
    except Exception as e:
        print(f"\n❌ Error saving configuration: {e}")
        return None


def test_ssh_connections(config_file):
    """Test SSH connections from configuration."""
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        ssh_servers = config.get('ssh_servers', [])
        if not ssh_servers:
            print("No SSH servers configured to test.")
            return
        
        print("\n" + "="*60)
        print("TESTING SSH CONNECTIONS")
        print("="*60)
        
        try:
            import paramiko
        except ImportError:
            print("❌ paramiko not installed. Cannot test SSH connections.")
            print("Install with: pip install paramiko")
            return
        
        for server in ssh_servers:
            hostname = server.get('hostname', 'unknown')
            print(f"\nTesting connection to {hostname}...")
            
            try:
                ssh_client = paramiko.SSHClient()
                ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                connect_params = {
                    'hostname': hostname,
                    'port': server.get('port', 22),
                    'username': server.get('username'),
                    'timeout': 10
                }
                
                if 'key_file' in server:
                    connect_params['key_filename'] = server['key_file']
                elif 'password' in server:
                    connect_params['password'] = server['password']
                
                ssh_client.connect(**connect_params)
                
                # Test basic command
                stdin, stdout, stderr = ssh_client.exec_command('echo "Connection test successful"')
                result = stdout.read().decode().strip()
                
                ssh_client.close()
                print(f"✅ {hostname}: {result}")
                
            except Exception as e:
                print(f"❌ {hostname}: {str(e)}")
    
    except Exception as e:
        print(f"Error testing SSH connections: {e}")


def main():
    """Main setup function."""
    print("Tutorial Scanner Setup")
    print("=" * 40)
    
    print("\n1. Installing dependencies...")
    install_dependencies()
    
    print("\n2. Creating SSH setup guide...")
    create_ssh_key_guide()
    
    print("\n3. Configuration setup...")
    setup_config = input("Create configuration interactively? (y/n): ").lower().strip()
    
    if setup_config == 'y':
        config_file = create_interactive_config()
        
        if config_file:
            test_connections = input("\nTest SSH connections? (y/n): ").lower().strip()
            if test_connections == 'y':
                test_ssh_connections(config_file)
    else:
        # Create sample config
        from tutorial_scanner import create_sample_config
        create_sample_config()
    
    print("\n" + "="*60)
    print("SETUP COMPLETE!")
    print("="*60)
    print("\nNext steps:")
    print("1. Review and edit your configuration file")
    print("2. Set up SSH keys if using SSH servers (see SSH_SETUP_GUIDE.md)")
    print("3. Run the scanner: python tutorial_scanner.py")
    print("\nFor help: python tutorial_scanner.py --help")


if __name__ == "__main__":
    main()
