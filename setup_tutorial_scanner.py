#!/usr/bin/env python3
"""
Setup script for Tutorial Scanner

This script helps set up the tutorial scanner with proper dependencies
and configuration for your environment.
"""

import subprocess
import sys
import os
import json
from pathlib import Path


def install_dependencies():
    """Install required Python packages."""
    packages = [
        'paramiko',  # For SSH connections
        'tqdm',      # For progress bars
    ]
    
    print("Installing required dependencies...")
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            print(f"You can install manually with: pip install {package}")


def create_ssh_password_guide():
    """Create a guide for SSH password authentication setup."""
    guide_content = """
# SSH Password Authentication Guide for Tutorial Scanner

## Important: SSH Key Authentication Disabled

This tutorial scanner is configured to use **password authentication only**.
SSH key authentication has been disabled for security and simplicity.

## 1. SSH Server Requirements

Ensure your SSH servers allow password authentication:

### Check SSH Server Configuration
On the SSH server, check `/etc/ssh/sshd_config`:
```bash
# These settings should be enabled:
PasswordAuthentication yes
PubkeyAuthentication no  # Keys are disabled
ChallengeResponseAuthentication no
```

### Restart SSH Service (if changes made)
```bash
sudo systemctl restart sshd
```

## 2. Test SSH Connection

Test password authentication manually:
```bash
ssh -o PreferredAuthentications=password -o PubkeyAuthentication=no username@server_hostname
```

## 3. Configuration Format

Use this format in your tutorial scanner config:
```json
{
  "ssh_servers": [
    {
      "hostname": "*************",
      "username": "your_username",
      "password": "your_password",
      "port": 22,
      "directories": ["/path/to/scan"]
    }
  ]
}
```

## 4. Security Best Practices

1. **Use strong passwords** (12+ characters, mixed case, numbers, symbols)
2. **Change default passwords** on all servers
3. **Restrict SSH access** by IP if possible
4. **Use non-standard SSH ports** (change from 22)
5. **Enable fail2ban** to prevent brute force attacks
6. **Protect config file**: `chmod 600 tutorial_scanner_config.json`

## 5. Network Security

### Firewall Configuration
```bash
# Allow SSH from specific IP only
sudo ufw allow from ***********/24 to any port 22
```

### SSH Port Change
In `/etc/ssh/sshd_config`:
```
Port 2222  # Change from default 22
```

Then in scanner config:
```json
{
  "hostname": "server.example.com",
  "port": 2222,
  "username": "user",
  "password": "password"
}
```

## 6. Troubleshooting

### Connection Issues:
```bash
# Test with verbose output
ssh -v -o PreferredAuthentications=password username@hostname

# Check if server allows password auth
ssh -o PreferredAuthentications=password -o PubkeyAuthentication=no username@hostname
```

### Common Errors:
- **"Permission denied"**: Check username/password
- **"Connection refused"**: Check hostname/port
- **"Host key verification failed"**: Accept host key or clear known_hosts

### Test SFTP Access:
```bash
sftp -o PreferredAuthentications=password username@hostname
```

## 7. Why Password-Only?

- **Simplicity**: No key generation or management
- **Consistency**: Same authentication method across all servers
- **No key conflicts**: Avoids SSH agent and key file issues
- **Easy deployment**: Just username/password needed
"""
    
    try:
        with open('SSH_PASSWORD_GUIDE.md', 'w') as f:
            f.write(guide_content)
        print("✅ SSH password guide created: SSH_PASSWORD_GUIDE.md")
    except Exception as e:
        print(f"❌ Error creating SSH guide: {e}")


def create_interactive_config():
    """Create configuration file interactively."""
    print("\n" + "="*60)
    print("INTERACTIVE CONFIGURATION SETUP")
    print("="*60)
    
    config = {
        "ssh_servers": [],
        "local_directories": [],
        "file_extensions": [
            ".pdf", ".mp4", ".mkv", ".avi", ".mov", ".wmv",
            ".txt", ".md", ".rst", ".docx", ".pptx", ".epub",
            ".zip", ".rar", ".7z"
        ],
        "output_file": "tutorial_inventory.csv",
        "separator": "|~|",
        "hash_algorithm": "md5",
        "max_file_size_mb": 10000,
        "ssh_timeout": 30,
        "chunk_size": 8192,
        "log_level": "INFO"
    }
    
    # SSH Servers
    print("\n1. SSH Server Configuration")
    print("-" * 30)
    
    while True:
        add_ssh = input("Add an SSH server? (y/n): ").lower().strip()
        if add_ssh != 'y':
            break
        
        server = {}
        server['hostname'] = input("  Hostname/IP: ").strip()
        server['username'] = input("  Username: ").strip()
        
        # Only password authentication is supported
        server['password'] = input("  Password: ").strip()
        
        port = input("  Port [22]: ").strip()
        server['port'] = int(port) if port else 22
        
        print("  Directories to scan (one per line, empty line to finish):")
        directories = []
        while True:
            directory = input("    Directory: ").strip()
            if not directory:
                break
            directories.append(directory)
        server['directories'] = directories if directories else ['/']
        
        config['ssh_servers'].append(server)
        print(f"  ✅ Added server: {server['hostname']}")
    
    # Local Directories
    print("\n2. Local Directory Configuration")
    print("-" * 35)
    
    print("Local directories to scan (one per line, empty line to finish):")
    while True:
        directory = input("  Directory: ").strip()
        if not directory:
            break
        if os.path.exists(directory):
            config['local_directories'].append(directory)
            print(f"  ✅ Added: {directory}")
        else:
            print(f"  ⚠️  Directory not found: {directory}")
            add_anyway = input("    Add anyway? (y/n): ").lower().strip()
            if add_anyway == 'y':
                config['local_directories'].append(directory)
    
    if not config['local_directories']:
        config['local_directories'] = ['.']  # Current directory as default
    
    # File Extensions
    print("\n3. File Extensions")
    print("-" * 20)
    print("Current extensions:", ', '.join(config['file_extensions']))
    
    modify_ext = input("Modify file extensions? (y/n): ").lower().strip()
    if modify_ext == 'y':
        print("Enter extensions (one per line, empty line to finish):")
        extensions = []
        while True:
            ext = input("  Extension (e.g., .pdf): ").strip()
            if not ext:
                break
            if not ext.startswith('.'):
                ext = '.' + ext
            extensions.append(ext.lower())
        
        if extensions:
            config['file_extensions'] = extensions
    
    # Output Configuration
    print("\n4. Output Configuration")
    print("-" * 25)
    
    output_file = input(f"Output file [{config['output_file']}]: ").strip()
    if output_file:
        config['output_file'] = output_file
    
    separator = input(f"CSV separator [{config['separator']}]: ").strip()
    if separator:
        config['separator'] = separator
    
    # Save configuration
    config_file = "tutorial_scanner_config.json"
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"\n✅ Configuration saved to: {config_file}")
        return config_file
    except Exception as e:
        print(f"\n❌ Error saving configuration: {e}")
        return None


def test_ssh_connections(config_file):
    """Test SSH connections from configuration."""
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        ssh_servers = config.get('ssh_servers', [])
        if not ssh_servers:
            print("No SSH servers configured to test.")
            return
        
        print("\n" + "="*60)
        print("TESTING SSH CONNECTIONS")
        print("="*60)
        
        try:
            import paramiko
        except ImportError:
            print("❌ paramiko not installed. Cannot test SSH connections.")
            print("Install with: pip install paramiko")
            return
        
        for server in ssh_servers:
            hostname = server.get('hostname', 'unknown')
            print(f"\nTesting connection to {hostname}...")
            
            try:
                ssh_client = paramiko.SSHClient()
                ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                connect_params = {
                    'hostname': hostname,
                    'port': server.get('port', 22),
                    'username': server.get('username'),
                    'timeout': 10
                }
                
                if 'key_file' in server:
                    connect_params['key_filename'] = server['key_file']
                elif 'password' in server:
                    connect_params['password'] = server['password']
                
                ssh_client.connect(**connect_params)
                
                # Test basic command
                stdin, stdout, stderr = ssh_client.exec_command('echo "Connection test successful"')
                result = stdout.read().decode().strip()
                
                ssh_client.close()
                print(f"✅ {hostname}: {result}")
                
            except Exception as e:
                print(f"❌ {hostname}: {str(e)}")
    
    except Exception as e:
        print(f"Error testing SSH connections: {e}")


def main():
    """Main setup function."""
    print("Tutorial Scanner Setup")
    print("=" * 40)
    
    print("\n1. Installing dependencies...")
    install_dependencies()
    
    print("\n2. Creating SSH password guide...")
    create_ssh_password_guide()
    
    print("\n3. Configuration setup...")
    setup_config = input("Create configuration interactively? (y/n): ").lower().strip()
    
    if setup_config == 'y':
        config_file = create_interactive_config()
        
        if config_file:
            test_connections = input("\nTest SSH connections? (y/n): ").lower().strip()
            if test_connections == 'y':
                test_ssh_connections(config_file)
    else:
        # Create sample config
        from tutorial_scanner import create_sample_config
        create_sample_config()
    
    print("\n" + "="*60)
    print("SETUP COMPLETE!")
    print("="*60)
    print("\nNext steps:")
    print("1. Review and edit your configuration file")
    print("2. Configure SSH password authentication (see SSH_PASSWORD_GUIDE.md)")
    print("3. Run the scanner: python tutorial_scanner.py")
    print("\nFor help: python tutorial_scanner.py --help")


if __name__ == "__main__":
    main()
