#!/usr/bin/env python3
"""
Test script for the directory-level tutorial scanner.

Creates test directory structures and demonstrates the directory analysis capabilities.
"""

import os
import tempfile
import shutil
import json
import time
from pathlib import Path


def create_test_directory_structure():
    """Create a comprehensive test directory structure."""
    test_dir = tempfile.mkdtemp(prefix="directory_scanner_test_")
    print(f"Creating test directory structure in: {test_dir}")
    
    # Create realistic directory structure
    directories = [
        "Programming/Python/Beginner",
        "Programming/Python/Advanced", 
        "Programming/JavaScript/Frontend",
        "Programming/JavaScript/Backend",
        "Design/Photoshop/Basics",
        "Design/Photoshop/Advanced",
        "Design/Illustrator/Vector_Graphics",
        "Languages/Spanish/Grammar",
        "Languages/Spanish/Conversation",
        "Languages/French/Beginner",
        "Math/Calculus/Derivatives",
        "Math/Statistics/Probability",
        "Science/Physics/Mechanics",
        "Science/Chemistry/Organic",
        "Business/Marketing/Digital",
        "Business/Finance/Accounting"
    ]
    
    # Create directories
    for directory in directories:
        full_path = Path(test_dir) / directory
        full_path.mkdir(parents=True, exist_ok=True)
    
    # Create files with realistic content and sizes
    test_files = [
        # Programming tutorials
        ("Programming/Python/Beginner/python_basics.pdf", "PDF content for Python basics", 50),
        ("Programming/Python/Beginner/variables_tutorial.mp4", "Video content for variables", 200),
        ("Programming/Python/Beginner/exercises.txt", "Text exercises for Python", 10),
        ("Programming/Python/Advanced/decorators.pdf", "Advanced Python decorators", 75),
        ("Programming/Python/Advanced/async_programming.mp4", "Async programming video", 300),
        
        ("Programming/JavaScript/Frontend/react_basics.pdf", "React fundamentals PDF", 60),
        ("Programming/JavaScript/Frontend/component_tutorial.mp4", "React components video", 250),
        ("Programming/JavaScript/Backend/nodejs_intro.pdf", "Node.js introduction", 45),
        ("Programming/JavaScript/Backend/express_tutorial.mp4", "Express.js tutorial", 180),
        
        # Design tutorials
        ("Design/Photoshop/Basics/layers_tutorial.pdf", "Photoshop layers guide", 40),
        ("Design/Photoshop/Basics/photo_editing.mp4", "Photo editing video", 220),
        ("Design/Photoshop/Advanced/advanced_techniques.pdf", "Advanced Photoshop", 80),
        ("Design/Photoshop/Advanced/compositing.mp4", "Photo compositing video", 350),
        
        ("Design/Illustrator/Vector_Graphics/pen_tool.pdf", "Pen tool tutorial", 35),
        ("Design/Illustrator/Vector_Graphics/logo_design.mp4", "Logo design video", 280),
        
        # Language tutorials
        ("Languages/Spanish/Grammar/verb_conjugation.pdf", "Spanish verb conjugation", 30),
        ("Languages/Spanish/Grammar/pronunciation.mp4", "Spanish pronunciation", 150),
        ("Languages/Spanish/Conversation/daily_phrases.pdf", "Daily Spanish phrases", 25),
        ("Languages/Spanish/Conversation/practice_dialogs.mp4", "Conversation practice", 200),
        
        ("Languages/French/Beginner/alphabet.pdf", "French alphabet guide", 20),
        ("Languages/French/Beginner/basic_words.mp4", "Basic French vocabulary", 120),
        
        # Math and Science
        ("Math/Calculus/Derivatives/limits.pdf", "Calculus limits tutorial", 55),
        ("Math/Calculus/Derivatives/chain_rule.mp4", "Chain rule explanation", 180),
        ("Math/Statistics/Probability/distributions.pdf", "Probability distributions", 65),
        
        ("Science/Physics/Mechanics/newton_laws.pdf", "Newton's laws of motion", 45),
        ("Science/Physics/Mechanics/kinematics.mp4", "Kinematics video tutorial", 190),
        ("Science/Chemistry/Organic/reactions.pdf", "Organic chemistry reactions", 70),
        
        # Business tutorials
        ("Business/Marketing/Digital/seo_basics.pdf", "SEO fundamentals", 40),
        ("Business/Marketing/Digital/social_media.mp4", "Social media marketing", 160),
        ("Business/Finance/Accounting/bookkeeping.pdf", "Basic bookkeeping", 50),
        ("Business/Finance/Accounting/financial_statements.mp4", "Financial statements", 210),
        
        # Create some duplicates for testing
        ("Programming/Python/Beginner/python_basics_copy.pdf", "PDF content for Python basics", 50),  # Duplicate
        ("Design/Backup/python_basics.pdf", "PDF content for Python basics", 50),  # Duplicate in different location
    ]
    
    # Create backup directory
    backup_dir = Path(test_dir) / "Design/Backup"
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # Write test files
    for file_path, base_content, size_kb in test_files:
        full_path = Path(test_dir) / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create content of specified size
        content = base_content + "\n" + "X" * (size_kb * 1024 - len(base_content) - 1)
        
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    print(f"Created {len(test_files)} test files across {len(directories)} directories")
    return test_dir


def create_test_config(test_dir):
    """Create test configuration for directory scanner."""
    config = {
        "ssh_servers": [],  # No SSH for testing
        "local_directories": [test_dir],
        "file_extensions": [
            ".pdf", ".mp4", ".mkv", ".avi", ".txt", ".md", ".docx", ".epub"
        ],
        "separator": "|~|",
        "ssh_timeout": 30,
        "incremental_scan": True,
        "max_directory_age_hours": 1,  # Short age for testing
        "parallel_processing": True,
        "log_level": "INFO"
    }
    
    config_file = "test_directory_config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Created test configuration: {config_file}")
    return config_file


def test_directory_scanner():
    """Test the directory scanner functionality."""
    print("="*60)
    print("DIRECTORY SCANNER TEST")
    print("="*60)
    
    # Create test environment
    test_dir = create_test_directory_structure()
    config_file = create_test_config(test_dir)
    
    try:
        # Import and test the directory scanner
        from tutorial_scanner import DirectoryScanner
        
        print(f"\nInitializing directory scanner...")
        scanner = DirectoryScanner(config_file, update_interval_minutes=1)  # 1 minute for testing
        
        print(f"Output files will be:")
        print(f"  - Main analysis: {scanner.main_output}")
        print(f"  - Progress tracking: {scanner.progress_output}")
        print(f"  - Potential duplicates: {scanner.duplicates_output}")
        
        # Test single directory scan
        print(f"\nTesting single directory scan...")
        test_directory = os.path.join(test_dir, "Programming/Python/Beginner")
        dir_info = scanner.scan_directory(test_directory, "local")
        
        print(f"✅ Directory scan completed:")
        print(f"   Path: {dir_info.directory_path}")
        print(f"   Total files: {dir_info.total_files}")
        print(f"   Videos: {dir_info.file_count_videos}")
        print(f"   Documents: {dir_info.file_count_documents}")
        print(f"   Total size: {dir_info.total_size_human}")
        print(f"   Status: {dir_info.scan_status}")
        print(f"   Duration: {dir_info.scan_duration_seconds:.2f}s")
        
        # Test full directory collection
        print(f"\nTesting directory collection...")
        all_directories = scanner._collect_all_directories()
        total_dirs = sum(len(dirs) for dirs in all_directories.values())
        print(f"✅ Found {total_dirs} directories to scan")
        
        # Test scanning multiple directories
        print(f"\nTesting multiple directory scanning...")
        scan_count = 0
        for server, directories in all_directories.items():
            for directory in directories[:3]:  # Limit to first 3 for testing
                dir_key = f"{server}:{directory}"
                dir_info = scanner.scan_directory(directory, server)
                scanner.directory_data[dir_key] = dir_info
                scan_count += 1
                print(f"   Scanned: {directory} ({dir_info.total_files} files, {dir_info.total_size_human})")
        
        print(f"✅ Scanned {scan_count} directories")
        
        # Test CSV output
        print(f"\nTesting CSV output generation...")
        scanner._write_final_results()
        
        # Check if files were created
        output_files = [scanner.main_output, scanner.progress_output]
        for output_file in output_files:
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                print(f"✅ Created {output_file} ({size} bytes)")
            else:
                print(f"❌ Failed to create {output_file}")
        
        # Check duplicates file
        if os.path.exists(scanner.duplicates_output):
            size = os.path.getsize(scanner.duplicates_output)
            print(f"✅ Created {scanner.duplicates_output} ({size} bytes)")
        else:
            print(f"ℹ️  No duplicates file created (no duplicates found)")
        
        # Display sample results
        print(f"\nSample directory analysis results:")
        for i, (dir_key, dir_info) in enumerate(list(scanner.directory_data.items())[:3]):
            print(f"  {i+1}. {dir_key}")
            print(f"     Files: {dir_info.total_files} (Videos: {dir_info.file_count_videos}, Docs: {dir_info.file_count_documents})")
            print(f"     Size: {dir_info.total_size_human}")
            print(f"     Status: {dir_info.scan_status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        try:
            shutil.rmtree(test_dir)
            os.remove(config_file)
            
            # Remove test output files
            for file in ['directory_analysis.csv', 'scan_progress.csv', 'potential_duplicates.csv', 
                        'scanner_state.pkl', 'directory_scanner.log']:
                if os.path.exists(file):
                    os.remove(file)
            
            print(f"\n🧹 Cleaned up test files")
        except Exception as e:
            print(f"Warning: Cleanup error: {e}")


def main():
    """Run the directory scanner test."""
    print("Directory Scanner Test Suite")
    print("=" * 40)
    
    success = test_directory_scanner()
    
    print(f"\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    if success:
        print("✅ Directory scanner test PASSED!")
        print("\nThe directory scanner can:")
        print("  ✅ Scan directories and analyze file types")
        print("  ✅ Calculate directory sizes and file counts")
        print("  ✅ Generate CSV reports with directory-level data")
        print("  ✅ Track scan progress and status")
        print("  ✅ Handle incremental scanning")
        print("  ✅ Detect potential duplicate directories")
        
        print(f"\nTo use the directory scanner:")
        print(f"  python tutorial_scanner.py --directory-mode")
        print(f"  python tutorial_scanner.py --directory-mode --single-cycle")
        print(f"  python tutorial_scanner.py --directory-mode --update-interval 15")
        
        return 0
    else:
        print("❌ Directory scanner test FAILED!")
        return 1


if __name__ == "__main__":
    exit(main())
