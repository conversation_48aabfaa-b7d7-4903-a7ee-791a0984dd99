#!/usr/bin/env python3
"""
Performance test for multicore tutorial scanner.

Creates a large test dataset and compares sequential vs parallel performance.
"""

import os
import tempfile
import shutil
import json
import time
import multiprocessing as mp
from pathlib import Path
from tutorial_scanner import TutorialScanner


def create_large_test_dataset(num_files=100):
    """Create a large test dataset for performance testing."""
    test_dir = tempfile.mkdtemp(prefix="tutorial_perf_test_")
    print(f"Creating {num_files} test files in: {test_dir}")
    
    # Create directory structure
    subdirs = [
        "programming/python",
        "programming/javascript", 
        "programming/java",
        "programming/cpp",
        "design/photoshop",
        "design/illustrator",
        "design/blender",
        "languages/spanish",
        "languages/french",
        "languages/german",
        "math/calculus",
        "math/statistics",
        "science/physics",
        "science/chemistry",
        "business/marketing",
        "business/finance"
    ]
    
    for subdir in subdirs:
        os.makedirs(os.path.join(test_dir, subdir), exist_ok=True)
    
    # File extensions to create
    extensions = ['.pdf', '.mp4', '.mkv', '.txt', '.md', '.docx', '.epub']
    
    # Create test files
    for i in range(num_files):
        # Choose random directory and extension
        subdir = subdirs[i % len(subdirs)]
        ext = extensions[i % len(extensions)]
        
        filename = f"tutorial_{i:04d}{ext}"
        file_path = os.path.join(test_dir, subdir, filename)
        
        # Create content of varying sizes
        content_size = 1000 + (i * 100)  # Varying content sizes
        content = f"Tutorial content {i}\n" + "X" * content_size
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    print(f"Created {num_files} test files")
    return test_dir


def create_test_config(test_dir, parallel=True, workers=None):
    """Create test configuration."""
    config = {
        "ssh_servers": [],  # No SSH for performance testing
        "local_directories": [test_dir],
        "file_extensions": [
            ".pdf", ".mp4", ".mkv", ".avi", ".txt", ".md", ".docx", ".epub"
        ],
        "output_file": f"perf_test_{'parallel' if parallel else 'sequential'}.csv",
        "separator": "|~|",
        "hash_algorithm": "md5",
        "max_file_size_mb": 10000,
        "parallel_processing": parallel,
        "max_workers": workers,
        "log_level": "WARNING"  # Reduce log noise during testing
    }
    
    config_file = f"perf_test_config_{'parallel' if parallel else 'sequential'}.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    return config_file


def run_performance_test(test_dir, num_files):
    """Run performance comparison between sequential and parallel processing."""
    print("="*60)
    print("TUTORIAL SCANNER PERFORMANCE TEST")
    print("="*60)
    print(f"Test dataset: {num_files} files")
    print(f"CPU cores: {mp.cpu_count()}")
    print("-" * 60)
    
    results = {}
    
    # Test 1: Sequential processing
    print("\n1. Testing SEQUENTIAL processing...")
    config_file_seq = create_test_config(test_dir, parallel=False)
    
    try:
        scanner_seq = TutorialScanner(config_file_seq, unique_output=False)
        
        start_time = time.time()
        scanner_seq.scan_all()
        end_time = time.time()
        
        seq_duration = end_time - start_time
        seq_files = len(scanner_seq.files_found)
        
        results['sequential'] = {
            'duration': seq_duration,
            'files': seq_files,
            'rate': seq_files / seq_duration if seq_duration > 0 else 0
        }
        
        print(f"✅ Sequential: {seq_files} files in {seq_duration:.2f}s ({seq_files/seq_duration:.1f} files/sec)")
        
    except Exception as e:
        print(f"❌ Sequential test failed: {e}")
        results['sequential'] = None
    
    finally:
        if os.path.exists(config_file_seq):
            os.remove(config_file_seq)
    
    # Test 2: Parallel processing (auto workers)
    print("\n2. Testing PARALLEL processing (auto workers)...")
    config_file_par = create_test_config(test_dir, parallel=True)
    
    try:
        scanner_par = TutorialScanner(config_file_par, unique_output=False)
        
        start_time = time.time()
        scanner_par.scan_all()
        end_time = time.time()
        
        par_duration = end_time - start_time
        par_files = len(scanner_par.files_found)
        
        results['parallel_auto'] = {
            'duration': par_duration,
            'files': par_files,
            'rate': par_files / par_duration if par_duration > 0 else 0,
            'workers': scanner_par.config.get('max_workers', mp.cpu_count())
        }
        
        print(f"✅ Parallel (auto): {par_files} files in {par_duration:.2f}s ({par_files/par_duration:.1f} files/sec)")
        
    except Exception as e:
        print(f"❌ Parallel test failed: {e}")
        results['parallel_auto'] = None
    
    finally:
        if os.path.exists(config_file_par):
            os.remove(config_file_par)
    
    # Test 3: Parallel processing (max workers)
    max_workers = mp.cpu_count()
    print(f"\n3. Testing PARALLEL processing ({max_workers} workers)...")
    config_file_max = create_test_config(test_dir, parallel=True, workers=max_workers)
    
    try:
        scanner_max = TutorialScanner(config_file_max, unique_output=False)
        
        start_time = time.time()
        scanner_max.scan_all()
        end_time = time.time()
        
        max_duration = end_time - start_time
        max_files = len(scanner_max.files_found)
        
        results['parallel_max'] = {
            'duration': max_duration,
            'files': max_files,
            'rate': max_files / max_duration if max_duration > 0 else 0,
            'workers': max_workers
        }
        
        print(f"✅ Parallel (max): {max_files} files in {max_duration:.2f}s ({max_files/max_duration:.1f} files/sec)")
        
    except Exception as e:
        print(f"❌ Parallel max test failed: {e}")
        results['parallel_max'] = None
    
    finally:
        if os.path.exists(config_file_max):
            os.remove(config_file_max)
    
    # Analysis
    print("\n" + "="*60)
    print("PERFORMANCE ANALYSIS")
    print("="*60)
    
    if results['sequential'] and results['parallel_auto']:
        speedup_auto = results['parallel_auto']['rate'] / results['sequential']['rate']
        print(f"Parallel (auto) speedup: {speedup_auto:.1f}x faster")
    
    if results['sequential'] and results['parallel_max']:
        speedup_max = results['parallel_max']['rate'] / results['sequential']['rate']
        print(f"Parallel (max) speedup: {speedup_max:.1f}x faster")
    
    # Efficiency calculation
    if results['parallel_max']:
        workers = results['parallel_max']['workers']
        efficiency = results['parallel_max']['rate'] / (results['sequential']['rate'] * workers) if results['sequential'] else 0
        print(f"Parallel efficiency: {efficiency:.1%} (ideal: 100%)")
    
    # Recommendations
    print(f"\nRecommendations:")
    if results['sequential'] and results['parallel_auto']:
        if speedup_auto > 1.5:
            print("✅ Parallel processing provides significant benefit")
            print("   Recommended for large datasets")
        elif speedup_auto > 1.1:
            print("✅ Parallel processing provides moderate benefit")
            print("   Recommended for datasets with 50+ files")
        else:
            print("ℹ️  Parallel processing provides minimal benefit")
            print("   Sequential processing may be sufficient")
    
    return results


def main():
    """Run the performance test."""
    print("Tutorial Scanner Multicore Performance Test")
    print("=" * 50)
    
    # Test configurations
    test_sizes = [50, 100, 200]
    
    for num_files in test_sizes:
        print(f"\n{'#'*60}")
        print(f"TESTING WITH {num_files} FILES")
        print(f"{'#'*60}")
        
        # Create test dataset
        test_dir = create_large_test_dataset(num_files)
        
        try:
            # Run performance test
            results = run_performance_test(test_dir, num_files)
            
            # Clean up output files
            for pattern in ['perf_test_*.csv', 'tutorial_scanner.log']:
                import glob
                for file in glob.glob(pattern):
                    try:
                        os.remove(file)
                    except:
                        pass
        
        finally:
            # Clean up test directory
            shutil.rmtree(test_dir)
            print(f"Cleaned up test directory: {test_dir}")
    
    print(f"\n{'='*60}")
    print("PERFORMANCE TEST COMPLETE")
    print(f"{'='*60}")
    print("\nConclusions:")
    print("1. Parallel processing is most beneficial for large datasets (100+ files)")
    print("2. Performance scales with CPU cores and file processing complexity")
    print("3. Hash calculation is CPU-intensive and benefits from parallelization")
    print("4. Use --workers parameter to optimize for your specific hardware")
    
    print(f"\nTo use parallel processing in production:")
    print(f"  python tutorial_scanner.py --benchmark")
    print(f"  python tutorial_scanner.py --workers {mp.cpu_count()}")
    print(f"  python tutorial_scanner.py --no-parallel  # for comparison")


if __name__ == "__main__":
    # Set multiprocessing start method
    try:
        mp.set_start_method('spawn', force=True)
    except RuntimeError:
        pass
    
    main()
