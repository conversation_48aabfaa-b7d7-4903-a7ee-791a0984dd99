#!/usr/bin/env python3
"""
Test script for the PDF scanner functionality.
Creates sample files and tests the scanner features.
"""

import os
import tempfile
import shutil
from pathlib import Path
from pdf_scanner import (
    find_pdf_files, find_duplicates, generate_summary_report,
    write_to_csv, write_duplicates_report, write_summary_report
)


def create_test_files():
    """Create a temporary directory structure with test files."""
    # Create temporary directory
    test_dir = tempfile.mkdtemp(prefix="pdf_scanner_test_")
    print(f"Creating test directory: {test_dir}")
    
    # Create subdirectories
    subdir1 = Path(test_dir) / "documents"
    subdir2 = Path(test_dir) / "archive" / "old_docs"
    subdir1.mkdir(parents=True)
    subdir2.mkdir(parents=True)
    
    # Create some fake PDF files (just text files with .pdf extension for testing)
    test_files = [
        (subdir1 / "document1.pdf", "This is document 1 content"),
        (subdir1 / "document2.pdf", "This is document 2 content"),
        (subdir2 / "old_document.pdf", "This is an old document"),
        (subdir2 / "duplicate.pdf", "This is document 1 content"),  # Duplicate of document1
        (Path(test_dir) / "root_doc.pdf", "Root level document"),
        (Path(test_dir) / "another_duplicate.pdf", "This is document 1 content"),  # Another duplicate
    ]
    
    for file_path, content in test_files:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"Created: {file_path.name}")
    
    return test_dir


def test_pdf_scanner():
    """Test the PDF scanner functionality."""
    print("="*60)
    print("TESTING PDF SCANNER")
    print("="*60)
    
    # Create test files
    test_dir = create_test_files()
    
    try:
        # Test 1: Basic file finding
        print("\nTest 1: Finding PDF files...")
        pdf_files, checksum_map = find_pdf_files(test_dir, 'md5')
        print(f"Found {len(pdf_files)} PDF files")
        
        # Test 2: Duplicate detection
        print("\nTest 2: Detecting duplicates...")
        duplicates = find_duplicates(checksum_map)
        print(f"Found {len(duplicates)} groups of duplicates")
        
        for checksum, paths in duplicates.items():
            print(f"  Duplicate group ({len(paths)} files):")
            for path in paths:
                print(f"    - {os.path.basename(path)}")
        
        # Test 3: Summary generation
        print("\nTest 3: Generating summary...")
        summary = generate_summary_report(pdf_files, duplicates)
        print(f"Summary generated with {len(summary)} metrics")
        
        # Test 4: CSV output
        print("\nTest 4: Writing CSV reports...")
        csv_file = os.path.join(test_dir, "test_results.csv")
        write_to_csv(pdf_files, csv_file)
        
        if duplicates:
            duplicates_file = os.path.join(test_dir, "test_duplicates.csv")
            write_duplicates_report(duplicates, duplicates_file)
        
        summary_file = os.path.join(test_dir, "test_summary.json")
        write_summary_report(summary, summary_file)
        
        # Test 5: Display results
        print("\nTest 5: Results summary...")
        print(f"Total files: {summary['total_files']}")
        print(f"Unique files: {summary['unique_files']}")
        print(f"Duplicate files: {summary['duplicate_files']}")
        print(f"Total size: {summary['total_size_bytes']} bytes")
        
        if summary['largest_file']:
            print(f"Largest file: {summary['largest_file']['filename']}")
        if summary['smallest_file']:
            print(f"Smallest file: {summary['smallest_file']['filename']}")
        
        print(f"\nTest files created in: {test_dir}")
        print("You can examine the generated CSV and JSON files there.")
        
        return True
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        return False
    
    finally:
        # Ask user if they want to keep test files
        keep_files = input(f"\nKeep test files in {test_dir}? (y/N): ").lower().startswith('y')
        if not keep_files:
            shutil.rmtree(test_dir)
            print("Test files cleaned up.")


def test_command_line_interface():
    """Test the command line interface."""
    print("\n" + "="*60)
    print("COMMAND LINE INTERFACE EXAMPLES")
    print("="*60)
    
    print("\nTo test the command line interface, try these commands:")
    print("1. Basic scan:")
    print("   python pdf_scanner.py .")
    print("\n2. Scan with duplicate detection:")
    print("   python pdf_scanner.py . --duplicates")
    print("\n3. Full analysis with all reports:")
    print("   python pdf_scanner.py . --all-reports")
    print("\n4. Use SHA256 checksums:")
    print("   python pdf_scanner.py . --hash sha256 --all-reports")
    print("\n5. Custom output file:")
    print("   python pdf_scanner.py /path/to/pdfs -o my_analysis.csv --summary")


if __name__ == "__main__":
    # Run the tests
    success = test_pdf_scanner()
    
    if success:
        print("\n✅ All tests passed!")
        test_command_line_interface()
    else:
        print("\n❌ Tests failed!")
