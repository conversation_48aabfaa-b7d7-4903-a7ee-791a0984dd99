#!/usr/bin/env python3
"""
Test script to verify the separator fix works correctly.
"""

import tempfile
import os
from pathlib import Path
from pdf_scanner_multicore import find_pdf_files_parallel, write_to_csv, write_duplicates_report, find_duplicates
from csv_reader_utility import read_pdf_csv, read_with_pandas


def create_test_files_with_commas():
    """Create test files with problematic names containing commas."""
    test_dir = tempfile.mkdtemp(prefix="separator_test_")
    print(f"Creating test files in: {test_dir}")
    
    # Create files with problematic names
    test_files = [
        "Report, Q1 2024.pdf",
        "Smith, John - Resume.pdf", 
        "Data Analysis, Final Version, v2.pdf",
        "Meeting Notes, Jan 15, 2024.pdf",
        "Budget Report, Dept A, B, C.pdf"
    ]
    
    for i, filename in enumerate(test_files):
        file_path = Path(test_dir) / filename
        content = f"Test PDF content {i}\nFilename: {filename}\n" + "X" * (i * 100)
        
        with open(file_path, 'w') as f:
            f.write(content)
        
        print(f"Created: {filename}")
    
    return test_dir


def test_multichar_separator():
    """Test multi-character separator functionality."""
    print("="*60)
    print("TESTING MULTI-CHARACTER SEPARATOR FIX")
    print("="*60)
    
    # Create test files
    test_dir = create_test_files_with_commas()
    
    try:
        # Test with default multi-character separator
        print(f"\n1. Testing with default separator '|~|'...")
        pdf_files, checksum_map = find_pdf_files_parallel(test_dir, 'md5', max_workers=2)
        
        if not pdf_files:
            print("❌ No PDF files found!")
            return False
        
        print(f"✅ Found {len(pdf_files)} PDF files")
        
        # Test CSV writing
        csv_file = os.path.join(test_dir, "test_output.csv")
        write_to_csv(pdf_files, csv_file, separator="|~|")
        
        if not os.path.exists(csv_file):
            print("❌ CSV file was not created!")
            return False
        
        print("✅ CSV file created successfully")
        
        # Test CSV reading
        print(f"\n2. Testing CSV reading...")
        records = read_pdf_csv(csv_file, separator="|~|")
        
        if len(records) != len(pdf_files):
            print(f"❌ Record count mismatch: {len(records)} vs {len(pdf_files)}")
            return False
        
        print(f"✅ Successfully read {len(records)} records")
        
        # Verify file names with commas are preserved
        print(f"\n3. Verifying file names with commas...")
        for record in records:
            filename = record['filename']
            if ',' in filename:
                print(f"✅ Comma preserved in: {filename}")
        
        # Test duplicates report
        print(f"\n4. Testing duplicates report...")
        duplicates = find_duplicates(checksum_map)
        if duplicates:
            dup_file = os.path.join(test_dir, "test_duplicates.csv")
            write_duplicates_report(duplicates, dup_file, separator="|~|")
            print(f"✅ Duplicates report created")
        else:
            print("ℹ️  No duplicates found (expected for unique test files)")
        
        # Test pandas reading
        print(f"\n5. Testing pandas integration...")
        df = read_with_pandas(csv_file, separator="|~|")
        if df is not None and len(df) == len(pdf_files):
            print(f"✅ Pandas DataFrame created with {len(df)} rows")
        else:
            print("❌ Pandas reading failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(test_dir)
        print(f"\n🧹 Cleaned up test directory: {test_dir}")


def test_single_char_separator():
    """Test single-character separator for comparison."""
    print("\n" + "="*60)
    print("TESTING SINGLE-CHARACTER SEPARATOR")
    print("="*60)
    
    # Create test files
    test_dir = create_test_files_with_commas()
    
    try:
        # Test with pipe separator
        print(f"\n1. Testing with single-character separator '|'...")
        pdf_files, checksum_map = find_pdf_files_parallel(test_dir, 'md5', max_workers=2)
        
        # Test CSV writing
        csv_file = os.path.join(test_dir, "test_single_char.csv")
        write_to_csv(pdf_files, csv_file, separator="|")
        
        print("✅ Single-character separator works")
        
        # Test reading
        records = read_pdf_csv(csv_file, separator="|")
        print(f"✅ Read {len(records)} records with single-char separator")
        
        return True
        
    except Exception as e:
        print(f"❌ Single-char test failed: {e}")
        return False
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(test_dir)


def main():
    """Run all tests."""
    print("PDF Scanner Separator Fix Test")
    print("Testing with file names containing commas...")
    
    # Test multi-character separator
    multi_success = test_multichar_separator()
    
    # Test single-character separator
    single_success = test_single_char_separator()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    if multi_success:
        print("✅ Multi-character separator test: PASSED")
    else:
        print("❌ Multi-character separator test: FAILED")
    
    if single_success:
        print("✅ Single-character separator test: PASSED")
    else:
        print("❌ Single-character separator test: FAILED")
    
    if multi_success and single_success:
        print("\n🎉 All tests PASSED! The separator fix is working correctly.")
        print("\nYou can now safely use:")
        print("  - Default '|~|' separator for maximum compatibility")
        print("  - Single-character separators like '|' or tab")
        print("  - File names with commas will be handled correctly")
        return 0
    else:
        print("\n❌ Some tests FAILED. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
