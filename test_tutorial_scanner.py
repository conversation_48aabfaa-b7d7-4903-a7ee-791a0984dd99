#!/usr/bin/env python3
"""
Test script for Tutorial Scanner

Creates test files and verifies the scanner functionality.
"""

import os
import tempfile
import shutil
import json
from pathlib import Path
from tutorial_scanner import TutorialScanner


def create_test_environment():
    """Create a test environment with sample tutorial files."""
    test_dir = tempfile.mkdtemp(prefix="tutorial_scanner_test_")
    print(f"Creating test environment in: {test_dir}")
    
    # Create directory structure
    subdirs = [
        "programming/python",
        "programming/javascript", 
        "design/photoshop",
        "design/illustrator",
        "languages/spanish",
        "languages/french"
    ]
    
    for subdir in subdirs:
        os.makedirs(os.path.join(test_dir, subdir), exist_ok=True)
    
    # Create test files with various extensions
    test_files = [
        # Programming tutorials
        ("programming/python/Python Basics.pdf", "PDF tutorial content for Python basics"),
        ("programming/python/Advanced Python.mp4", "Video content for advanced Python" * 100),
        ("programming/python/Python Examples.txt", "Text examples for Python programming"),
        ("programming/javascript/JS Fundamentals.pdf", "JavaScript fundamentals PDF content"),
        ("programming/javascript/React Tutorial.mkv", "React video tutorial content" * 150),
        
        # Design tutorials  
        ("design/photoshop/Photo Editing.pdf", "Photoshop photo editing tutorial"),
        ("design/photoshop/Advanced Techniques.mp4", "Advanced Photoshop techniques video" * 200),
        ("design/illustrator/Vector Graphics.pdf", "Vector graphics tutorial content"),
        ("design/illustrator/Logo Design.avi", "Logo design video tutorial" * 180),
        
        # Language tutorials
        ("languages/spanish/Spanish Grammar.pdf", "Spanish grammar tutorial content"),
        ("languages/spanish/Conversation Practice.mp4", "Spanish conversation video" * 120),
        ("languages/french/French Basics.epub", "French basics ebook content"),
        ("languages/french/Pronunciation Guide.txt", "French pronunciation guide text"),
        
        # Duplicate files (same content, different locations)
        ("programming/python/Python Basics Copy.pdf", "PDF tutorial content for Python basics"),  # Duplicate
        ("design/backup/Python Basics.pdf", "PDF tutorial content for Python basics"),  # Duplicate
        
        # Files with problematic names
        ("programming/Tutorial, Advanced Python, Part 1.pdf", "Tutorial with commas in name"),
        ("design/Design Tutorial - Colors, Fonts & Layout.mp4", "Another file with commas"),
    ]
    
    # Create backup directory
    os.makedirs(os.path.join(test_dir, "design/backup"), exist_ok=True)
    
    # Write test files
    for file_path, content in test_files:
        full_path = os.path.join(test_dir, file_path)
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Created: {file_path}")
    
    return test_dir


def create_test_config(test_dir):
    """Create a test configuration file."""
    config = {
        "ssh_servers": [],  # No SSH servers for testing
        "local_directories": [test_dir],
        "file_extensions": [
            ".pdf", ".mp4", ".mkv", ".avi", ".txt", ".epub"
        ],
        "output_file": "test_tutorial_inventory.csv",
        "separator": "|~|",
        "hash_algorithm": "md5",
        "max_file_size_mb": 10000,
        "ssh_timeout": 30,
        "chunk_size": 8192,
        "log_level": "INFO"
    }
    
    config_file = "test_config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Created test configuration: {config_file}")
    return config_file


def run_scanner_test():
    """Run the tutorial scanner test."""
    print("="*60)
    print("TUTORIAL SCANNER TEST")
    print("="*60)
    
    # Create test environment
    test_dir = create_test_environment()
    config_file = create_test_config(test_dir)
    
    try:
        # Initialize and run scanner
        print(f"\nInitializing scanner with test configuration...")
        scanner = TutorialScanner(config_file)
        
        print(f"Starting scan of test directory...")
        scanner.scan_all()
        
        # Verify results
        print(f"\nVerifying results...")
        
        # Check if files were found
        total_files = len(scanner.files_found)
        print(f"✅ Found {total_files} tutorial files")
        
        if total_files == 0:
            print("❌ No files found - test failed")
            return False
        
        # Check for duplicates
        duplicates = scanner.find_duplicates()
        print(f"✅ Found {len(duplicates)} groups of duplicates")
        
        # Check if CSV was created
        csv_file = scanner.config['output_file']
        if os.path.exists(csv_file):
            print(f"✅ CSV report created: {csv_file}")
            
            # Check CSV content
            with open(csv_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"✅ CSV contains {len(lines)-1} data rows (plus header)")
        else:
            print(f"❌ CSV report not created")
            return False
        
        # Check for duplicates report
        dup_file = csv_file.replace('.csv', '_duplicates.csv')
        if os.path.exists(dup_file):
            print(f"✅ Duplicates report created: {dup_file}")
        
        # Verify file types
        extensions_found = set()
        for file_info in scanner.files_found:
            extensions_found.add(file_info['file_extension'])
        
        print(f"✅ File types found: {', '.join(sorted(extensions_found))}")
        
        # Check for files with commas in names
        comma_files = [f for f in scanner.files_found if ',' in f['filename']]
        if comma_files:
            print(f"✅ Handled {len(comma_files)} files with commas in names")
        
        # Display sample results
        print(f"\nSample files found:")
        for i, file_info in enumerate(scanner.files_found[:5]):
            print(f"  {i+1}. {file_info['filename']} ({file_info['file_size_human']})")
        
        if len(scanner.files_found) > 5:
            print(f"  ... and {len(scanner.files_found) - 5} more files")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        try:
            shutil.rmtree(test_dir)
            os.remove(config_file)
            
            # Remove test output files
            for file in ['test_tutorial_inventory.csv', 'test_tutorial_inventory_duplicates.csv', 'tutorial_scanner.log']:
                if os.path.exists(file):
                    os.remove(file)
            
            print(f"\n🧹 Cleaned up test files")
        except Exception as e:
            print(f"Warning: Cleanup error: {e}")


def test_csv_parsing():
    """Test CSV parsing with problematic file names."""
    print("\n" + "="*60)
    print("CSV PARSING TEST")
    print("="*60)
    
    try:
        from csv_reader_utility import read_pdf_csv
        
        # Create test CSV with problematic names
        test_csv_content = '''filename|~|full_path|~|source|~|file_size_bytes
Tutorial, Part 1.pdf|~|/path/Tutorial, Part 1.pdf|~|local|~|1024
Design - Colors, Fonts & Layout.mp4|~|/path/Design - Colors, Fonts & Layout.mp4|~|server1|~|2048
Normal File.txt|~|/path/Normal File.txt|~|local|~|512'''
        
        test_csv_file = "test_parsing.csv"
        with open(test_csv_file, 'w') as f:
            f.write(test_csv_content)
        
        # Test reading
        records = read_pdf_csv(test_csv_file, separator="|~|")
        
        if len(records) == 3:
            print("✅ CSV parsing test passed")
            print(f"✅ Successfully parsed {len(records)} records")
            
            # Check if commas are preserved
            comma_files = [r for r in records if ',' in r['filename']]
            print(f"✅ Preserved commas in {len(comma_files)} file names")
            
            return True
        else:
            print(f"❌ CSV parsing test failed: expected 3 records, got {len(records)}")
            return False
    
    except Exception as e:
        print(f"❌ CSV parsing test failed: {e}")
        return False
    
    finally:
        if os.path.exists("test_parsing.csv"):
            os.remove("test_parsing.csv")


def main():
    """Run all tests."""
    print("Tutorial Scanner Test Suite")
    print("=" * 40)
    
    # Test 1: Main scanner functionality
    scanner_success = run_scanner_test()
    
    # Test 2: CSV parsing
    csv_success = test_csv_parsing()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    if scanner_success:
        print("✅ Scanner functionality test: PASSED")
    else:
        print("❌ Scanner functionality test: FAILED")
    
    if csv_success:
        print("✅ CSV parsing test: PASSED")
    else:
        print("❌ CSV parsing test: FAILED")
    
    if scanner_success and csv_success:
        print("\n🎉 All tests PASSED! Tutorial scanner is working correctly.")
        print("\nYou can now:")
        print("1. Create your configuration: python tutorial_scanner.py --create-config")
        print("2. Run the scanner: python tutorial_scanner.py")
        print("3. Set up SSH servers if needed")
        return 0
    else:
        print("\n❌ Some tests FAILED. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
