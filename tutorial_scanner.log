2025-08-04 22:20:05,382 - INFO - Starting tutorial file scan with parallel processing
2025-08-04 22:20:05,382 - INFO - Using parallel processing: True
2025-08-04 22:20:05,382 - INFO - Max workers: 16
2025-08-04 22:20:05,382 - INFO - Scanning local directory: .
2025-08-04 22:20:05,383 - INFO - Found 3 files to process in .
2025-08-04 22:20:05,388 - INFO - Found 3 tutorial files in .
2025-08-04 22:20:05,388 - INFO - <PERSON><PERSON> completed in 0:00:00.005940
2025-08-04 22:20:05,388 - INFO - Total files found: 3
2025-08-04 22:20:05,388 - INFO - Found 0 groups of duplicate files
2025-08-04 22:20:05,388 - INFO - CSV report written to: tutorial_inventory.csv
2025-08-04 22:20:05,388 - INFO - Found 0 groups of duplicate files
2025-08-04 22:27:53,999 - INFO - Generated unique output filename: tutorial_inventory_20250804_222753_mint-Saints_b2ccc647.csv
2025-08-04 22:27:53,999 - INFO - Starting tutorial file scan with parallel processing
2025-08-04 22:27:53,999 - INFO - Using parallel processing: True
2025-08-04 22:27:53,999 - INFO - Max workers: 16
2025-08-04 22:27:53,999 - INFO - Connecting to SSH server: 192.168.1.101
2025-08-04 22:27:54,000 - INFO - Connecting to SSH server: 192.168.1.103
2025-08-04 22:27:54,000 - INFO - Connecting to SSH server: 192.168.1.104
2025-08-04 22:27:54,000 - INFO - Scanning local directory: /home/<USER>/
2025-08-04 22:27:54,021 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-04 22:27:54,111 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-04 22:27:56,177 - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-08-04 22:28:05,757 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:28:05,757 - ERROR - Traceback (most recent call last):
2025-08-04 22:28:05,758 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:28:05,758 - ERROR -     handler(m)
2025-08-04 22:28:05,758 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:28:05,758 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:28:05,758 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:28:05,758 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:28:05,758 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:28:05,758 - ERROR - 
2025-08-04 22:28:05,897 - ERROR - Error connecting to SSH server 192.168.1.104: No existing session
2025-08-04 22:28:05,897 - INFO - Found 0 tutorial files on 192.168.1.104
2025-08-04 22:28:06,500 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:28:06,500 - ERROR - Traceback (most recent call last):
2025-08-04 22:28:06,500 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:28:06,500 - ERROR -     handler(m)
2025-08-04 22:28:06,500 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:28:06,500 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:28:06,500 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:28:06,500 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:28:06,501 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:28:06,501 - ERROR - 
2025-08-04 22:28:06,639 - ERROR - Error connecting to SSH server 192.168.1.103: No existing session
2025-08-04 22:28:06,639 - INFO - Found 0 tutorial files on 192.168.1.103
