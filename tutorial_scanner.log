2025-08-04 22:20:05,382 - INFO - Starting tutorial file scan with parallel processing
2025-08-04 22:20:05,382 - INFO - Using parallel processing: True
2025-08-04 22:20:05,382 - INFO - Max workers: 16
2025-08-04 22:20:05,382 - INFO - Scanning local directory: .
2025-08-04 22:20:05,383 - INFO - Found 3 files to process in .
2025-08-04 22:20:05,388 - INFO - Found 3 tutorial files in .
2025-08-04 22:20:05,388 - INFO - <PERSON><PERSON> completed in 0:00:00.005940
2025-08-04 22:20:05,388 - INFO - Total files found: 3
2025-08-04 22:20:05,388 - INFO - Found 0 groups of duplicate files
2025-08-04 22:20:05,388 - INFO - CSV report written to: tutorial_inventory.csv
2025-08-04 22:20:05,388 - INFO - Found 0 groups of duplicate files
2025-08-04 22:27:53,999 - INFO - Generated unique output filename: tutorial_inventory_20250804_222753_mint-Saints_b2ccc647.csv
2025-08-04 22:27:53,999 - INFO - Starting tutorial file scan with parallel processing
2025-08-04 22:27:53,999 - INFO - Using parallel processing: True
2025-08-04 22:27:53,999 - INFO - Max workers: 16
2025-08-04 22:27:53,999 - INFO - Connecting to SSH server: 192.168.1.101
2025-08-04 22:27:54,000 - INFO - Connecting to SSH server: *************
2025-08-04 22:27:54,000 - INFO - Connecting to SSH server: 192.168.1.104
2025-08-04 22:27:54,000 - INFO - Scanning local directory: /home/<USER>/
2025-08-04 22:27:54,021 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-04 22:27:54,111 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-04 22:27:56,177 - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-08-04 22:28:05,757 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:28:05,757 - ERROR - Traceback (most recent call last):
2025-08-04 22:28:05,758 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:28:05,758 - ERROR -     handler(m)
2025-08-04 22:28:05,758 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:28:05,758 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:28:05,758 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:28:05,758 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:28:05,758 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:28:05,758 - ERROR - 
2025-08-04 22:28:05,897 - ERROR - Error connecting to SSH server 192.168.1.104: No existing session
2025-08-04 22:28:05,897 - INFO - Found 0 tutorial files on 192.168.1.104
2025-08-04 22:28:06,500 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:28:06,500 - ERROR - Traceback (most recent call last):
2025-08-04 22:28:06,500 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:28:06,500 - ERROR -     handler(m)
2025-08-04 22:28:06,500 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:28:06,500 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:28:06,500 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:28:06,500 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:28:06,501 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:28:06,501 - ERROR - 
2025-08-04 22:28:06,639 - ERROR - Error connecting to SSH server *************: No existing session
2025-08-04 22:28:06,639 - INFO - Found 0 tutorial files on *************
2025-08-04 22:28:07,092 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:28:07,093 - ERROR - Traceback (most recent call last):
2025-08-04 22:28:07,093 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:28:07,093 - ERROR -     handler(m)
2025-08-04 22:28:07,093 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:28:07,093 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:28:07,093 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:28:07,093 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:28:07,093 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:28:07,093 - ERROR - 
2025-08-04 22:28:07,231 - ERROR - Error connecting to SSH server 192.168.1.101: No existing session
2025-08-04 22:28:07,231 - INFO - Found 0 tutorial files on 192.168.1.101
2025-08-04 22:29:44,791 - INFO - Generated unique output filename: tutorial_inventory_20250804_222944_mint-Saints_664baef4.csv
2025-08-04 22:29:44,791 - INFO - Starting tutorial file scan with parallel processing
2025-08-04 22:29:44,791 - INFO - Using parallel processing: True
2025-08-04 22:29:44,791 - INFO - Max workers: 16
2025-08-04 22:29:44,791 - INFO - Connecting to SSH server: 192.168.1.101
2025-08-04 22:29:44,791 - INFO - Connecting to SSH server: *************
2025-08-04 22:29:44,792 - INFO - Connecting to SSH server: 192.168.1.104
2025-08-04 22:29:44,792 - INFO - Scanning local directory: /home/<USER>/
2025-08-04 22:29:44,809 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-04 22:29:44,817 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-04 22:29:45,061 - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-08-04 22:29:52,217 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:29:52,218 - ERROR - Traceback (most recent call last):
2025-08-04 22:29:52,218 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:29:52,218 - ERROR -     handler(m)
2025-08-04 22:29:52,218 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:29:52,218 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:29:52,218 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:29:52,218 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:29:52,218 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:29:52,218 - ERROR - 
2025-08-04 22:29:52,357 - ERROR - Error connecting to SSH server 192.168.1.104: No existing session
2025-08-04 22:29:52,357 - INFO - Found 0 tutorial files on 192.168.1.104
2025-08-04 22:29:53,216 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:29:53,217 - ERROR - Traceback (most recent call last):
2025-08-04 22:29:53,217 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:29:53,217 - ERROR -     handler(m)
2025-08-04 22:29:53,217 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:29:53,217 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:29:53,217 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:29:53,217 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:29:53,217 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:29:53,217 - ERROR - 
2025-08-04 22:29:53,357 - ERROR - Error connecting to SSH server *************: No existing session
2025-08-04 22:29:53,357 - INFO - Found 0 tutorial files on *************
2025-08-04 22:29:54,080 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:29:54,080 - ERROR - Traceback (most recent call last):
2025-08-04 22:29:54,080 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:29:54,080 - ERROR -     handler(m)
2025-08-04 22:29:54,080 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:29:54,080 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:29:54,080 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:29:54,080 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:29:54,080 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:29:54,081 - ERROR - 
2025-08-04 22:29:54,219 - ERROR - Error connecting to SSH server 192.168.1.101: No existing session
2025-08-04 22:29:54,219 - INFO - Found 0 tutorial files on 192.168.1.101
2025-08-04 22:42:56,345 - INFO - Found 44376 files to process in /home/<USER>/
2025-08-04 22:42:56,345 - INFO - Processing 44376 files with 16 workers
2025-08-04 22:43:05,741 - INFO - Found 44376 tutorial files in /home/<USER>/
2025-08-04 22:43:05,743 - INFO - Scanning local directory: /media/
2025-08-05 03:42:49,096 - INFO - Found 146645 files to process in /media/
2025-08-05 03:42:49,096 - INFO - Processing 146645 files with 16 workers
2025-08-05 09:09:40,661 - INFO - Generated unique output filename: tutorial_inventory2_20250805_090940_mint-Saints_14c1536f.csv
2025-08-05 09:09:40,661 - INFO - Starting tutorial file scan with parallel processing
2025-08-05 09:09:40,661 - INFO - Using parallel processing: True
2025-08-05 09:09:40,661 - INFO - Max workers: 16
2025-08-05 09:09:40,662 - INFO - Connecting to SSH server: 192.168.1.101
2025-08-05 09:09:40,662 - INFO - Connecting to SSH server: *************
2025-08-05 09:09:40,662 - INFO - Connecting to SSH server: 192.168.1.104
2025-08-05 09:09:40,662 - INFO - Scanning local directory: /home/<USER>/
2025-08-05 09:09:40,723 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-05 09:09:40,724 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-05 09:09:41,011 - INFO - Authentication (password) successful!
2025-08-05 09:09:41,017 - INFO - Authentication (password) successful!
2025-08-05 09:09:41,057 - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-08-05 09:09:41,102 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-08-05 09:09:41,102 - INFO - Scanning remote directory: 192.168.1.104:/home/<USER>
2025-08-05 09:09:41,117 - INFO - No tutorial files found in 192.168.1.104:/home/<USER>
2025-08-05 09:09:41,118 - INFO - Scanning remote directory: 192.168.1.104:/media/
2025-08-05 09:09:41,217 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-08-05 09:09:41,217 - INFO - Scanning remote directory: *************:/home/<USER>
2025-08-05 09:09:41,864 - INFO - Authentication (password) successful!
2025-08-05 09:09:42,039 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-08-05 09:09:42,039 - INFO - Scanning remote directory: 192.168.1.101:/home/<USER>
2025-08-05 09:17:55,797 - INFO - Found 279 files to process in /home/<USER>/
2025-08-05 09:17:55,797 - INFO - Processing 279 files with 16 workers
2025-08-05 09:18:00,375 - INFO - Found 279 tutorial files in /home/<USER>/
2025-08-05 09:18:00,375 - INFO - Scanning local directory: /media/
2025-08-05 10:35:18,049 - INFO - Scanning remote directory: *************:/media/
2025-08-05 10:35:18,069 - INFO - No tutorial files found in *************:/media/
2025-08-05 10:35:18,069 - INFO - [chan 0] sftp session closed.
2025-08-05 10:35:18,069 - INFO - Found 274 tutorial files on *************
2025-08-05 12:03:23,454 - INFO - Found 21317 files to process in /media/
2025-08-05 12:03:23,454 - INFO - Processing 21317 files with 16 workers
2025-08-05 18:59:39,531 - INFO - Generated unique output filename: tutorial_inventory2_20250805_185939_mint-Saints_96c086fe.csv
2025-08-05 18:59:39,531 - INFO - Starting tutorial file scan with parallel processing
2025-08-05 18:59:39,531 - INFO - Using parallel processing: True
2025-08-05 18:59:39,531 - INFO - Max workers: 16
2025-08-05 18:59:39,531 - ERROR - SSH functionality not available. Install paramiko.
2025-08-05 18:59:39,531 - ERROR - SSH functionality not available. Install paramiko.
2025-08-05 18:59:39,531 - ERROR - SSH functionality not available. Install paramiko.
2025-08-05 18:59:39,531 - INFO - Scanning local directory: /home/<USER>/
2025-08-05 19:00:40,536 - INFO - Found 277 files to process in /home/<USER>/
2025-08-05 19:00:40,536 - INFO - Processing 277 files with 16 workers
2025-08-05 19:00:45,346 - INFO - Found 277 tutorial files in /home/<USER>/
2025-08-05 19:00:45,346 - INFO - Scanning local directory: /media/
2025-08-05 19:36:04,223 - INFO - Found 21318 files to process in /media/
2025-08-05 19:36:04,223 - INFO - Processing 21318 files with 16 workers
2025-08-05 21:20:11,440 - INFO - Generated unique output filename: tutorial_inventory2_20250805_212011_mint-Saints_106bc325.csv
2025-08-05 21:20:11,440 - INFO - Starting tutorial file scan with parallel processing
2025-08-05 21:20:11,440 - INFO - Using parallel processing: True
2025-08-05 21:20:11,440 - INFO - Max workers: 16
2025-08-05 21:20:11,441 - INFO - Connecting to SSH server: 192.168.1.101
2025-08-05 21:20:11,441 - INFO - Connecting to SSH server: *************
2025-08-05 21:20:11,441 - INFO - Connecting to SSH server: 192.168.1.104
2025-08-05 21:20:11,441 - INFO - Scanning local directory: /home/<USER>/
2025-08-05 21:20:11,459 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-05 21:20:11,463 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-05 21:20:11,550 - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-08-05 21:20:11,584 - INFO - Authentication (password) successful!
2025-08-05 21:20:11,663 - INFO - Authentication (password) successful!
2025-08-05 21:20:11,709 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-08-05 21:20:11,709 - INFO - Scanning remote directory: 192.168.1.104:/home/<USER>
2025-08-05 21:20:11,735 - INFO - No tutorial files found in 192.168.1.104:/home/<USER>
2025-08-05 21:20:11,736 - INFO - Scanning remote directory: 192.168.1.104:/media/
2025-08-05 21:20:11,749 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-08-05 21:20:11,749 - INFO - Scanning remote directory: *************:/home/<USER>
2025-08-05 21:20:11,779 - INFO - Authentication (password) successful!
2025-08-05 21:20:11,911 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-08-05 21:20:11,911 - INFO - Scanning remote directory: 192.168.1.101:/home/<USER>
2025-08-05 21:25:21,057 - INFO - Found 277 files to process in /home/<USER>/
2025-08-05 21:25:21,057 - INFO - Processing 277 files with 16 workers
2025-08-05 21:25:25,758 - INFO - Found 277 tutorial files in /home/<USER>/
2025-08-05 21:25:25,759 - INFO - Scanning local directory: /media/
2025-08-05 22:47:09,600 - ERROR - Socket exception: Connection reset by peer (104)
2025-08-05 22:47:09,601 - WARNING - Error calculating hash for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/1. Promo to Advance Options Trading Course/1. Promotional Lecture on Advance Options Trading.mp4: Server connection dropped: 
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/9. Iron Butterfly Options Trading Strategy Details/6. Understanding Iron Butterfly Options Trading Strategy.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/9. Iron Butterfly Options Trading Strategy Details/3. Longfly Example.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/9. Iron Butterfly Options Trading Strategy Details/10. Short Iron Butterfly Trade BB Chart.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/9. Iron Butterfly Options Trading Strategy Details/2. What is Long Iron Butterfly.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/9. Iron Butterfly Options Trading Strategy Details/5. Breakeven points.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/9. Iron Butterfly Options Trading Strategy Details/7. Long Iron Butterfly Trade BB Chart.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/9. Iron Butterfly Options Trading Strategy Details/9. Short Iron Butterfly Example.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/9. Iron Butterfly Options Trading Strategy Details/8. What is Short Iron Butterfly.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/9. Iron Butterfly Options Trading Strategy Details/1. What is Iron Butterfly.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/9. Iron Butterfly Options Trading Strategy Details/12. Sum Up.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/9. Iron Butterfly Options Trading Strategy Details/11. Difference Btw Iron Condor and Iron Butterfly.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/12. Strangle Options Trading Strategy Details/8. Short Strangle on Charts.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/12. Strangle Options Trading Strategy Details/6. What is Short Strangle Options Trading Strategy.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/12. Strangle Options Trading Strategy Details/1. What is Strangle Options Trading Strategy.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/12. Strangle Options Trading Strategy Details/7. Short Strangle Example.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/12. Strangle Options Trading Strategy Details/9. Sumup.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/12. Strangle Options Trading Strategy Details/3. Long Strangle Options Trading Example.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/12. Strangle Options Trading Strategy Details/2. What is Long Strangle Options Trading Strategy.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/12. Strangle Options Trading Strategy Details/5. Long Strangle on Charts.mp4: Socket is closed
2025-08-05 22:47:09,601 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/7. Collar Options Trading Strategy/1. What is Collar Option Trading Strategy.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/7. Collar Options Trading Strategy/9. Why Collar Option Strategy.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/7. Collar Options Trading Strategy/2. What is Stock or FX Collar.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/7. Collar Options Trading Strategy/7. Interest Rate Derivative Collar.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/7. Collar Options Trading Strategy/5. Collar Option Trading On Charts.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/7. Collar Options Trading Strategy/3. Collar Options Trading Example.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/7. Collar Options Trading Strategy/10. Conclusion.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/7. Collar Options Trading Strategy/8. Interest Rate Derivative Collar Example.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/7. Cruz of this Course Part 1 - Strategy to profits.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/13. Options Terms Part B.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/12. Options Terms Part A.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/8. Cruz of this Course Part 2 - Strategy to profits.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/10. Yield Boost Part A.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/4. How Stock Market Operates.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/5. What are Options and its Historical background Part 1.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/9. Stock Options.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/6. What are Options and its Historical background Part 2.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/2. Scam.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/11. Yield Boost Part B.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/3. Scam Effects on You.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/15. Conclusion.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/14. Options Terms Part C.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/15. How to pick Stocks for Options Trading/1. Current Orders placing Procedure - the devil within.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/13. Live Trade on Strangle Options Trading Strategy/2. Long Strangle CSCO - Live Trade.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/13. Live Trade on Strangle Options Trading Strategy/1. Short Strangle BIIB - Live trade.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/13. Live Trade on Strangle Options Trading Strategy/3. Long Strangle ABT Bull Live Trade2.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/11. Strangle Options Trading Strategy Course/1. Promo to Strangle Options Trading Course.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/11. Strangle Options Trading Strategy Course/2. Introduction to Strangle Options Trading Course.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/DIY Advance Options Trading Strategies (5 Courses) 11 Hours/[Tutsgalaxy.com] - DIY Advance Options Trading Strategies (5 Courses) 11 Hours/2. Risk Reversal Options Trading Strategy Course/1. Introduction to Risk Reversal Options Trading Course.mp4: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/Desktop/books/Learn_AI-assisted_Python_Programming_With_GitHub_Copilot_and_ChatGPT.zip: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/.config/calibre/plugins/Extract ISBN.zip: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/.config/calibre/plugins/Find Duplicates.zip: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/.config/JetBrains/PyCharmCE2024.2/tasks/AutoTrader.tasks.zip: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/.config/JetBrains/PyCharmCE2024.2/tasks/AutoTrader.contexts.zip: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/.config/JetBrains/PyCharmCE2024.2/tasks/pythonProject1.tasks.zip: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/.config/JetBrains/PyCharmCE2024.2/tasks/pythonProject1.contexts.zip: Socket is closed
2025-08-05 22:47:09,602 - WARNING - Error getting file info for /home/<USER>/.config/google-chrome/Profile 1/Extensions/gojbdfnpnhogfdgjbigejoaolejmgdhk/3.9.7_0/OneNoteWebClipper.zip: Socket is closed
2025-08-05 22:47:09,603 - INFO - Scanning remote directory: *************:/media/
2025-08-05 22:47:09,603 - ERROR - Error scanning remote directory *************:/media/: SSH session not active
2025-08-05 22:47:09,603 - INFO - [chan 0] sftp session closed.
2025-08-05 22:47:09,603 - INFO - Found 218 tutorial files on *************
