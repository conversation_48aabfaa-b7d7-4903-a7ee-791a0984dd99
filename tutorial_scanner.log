2025-08-04 22:20:05,382 - INFO - Starting tutorial file scan with parallel processing
2025-08-04 22:20:05,382 - INFO - Using parallel processing: True
2025-08-04 22:20:05,382 - INFO - Max workers: 16
2025-08-04 22:20:05,382 - INFO - Scanning local directory: .
2025-08-04 22:20:05,383 - INFO - Found 3 files to process in .
2025-08-04 22:20:05,388 - INFO - Found 3 tutorial files in .
2025-08-04 22:20:05,388 - INFO - <PERSON><PERSON> completed in 0:00:00.005940
2025-08-04 22:20:05,388 - INFO - Total files found: 3
2025-08-04 22:20:05,388 - INFO - Found 0 groups of duplicate files
2025-08-04 22:20:05,388 - INFO - CSV report written to: tutorial_inventory.csv
2025-08-04 22:20:05,388 - INFO - Found 0 groups of duplicate files
2025-08-04 22:27:53,999 - INFO - Generated unique output filename: tutorial_inventory_20250804_222753_mint-Saints_b2ccc647.csv
2025-08-04 22:27:53,999 - INFO - Starting tutorial file scan with parallel processing
2025-08-04 22:27:53,999 - INFO - Using parallel processing: True
2025-08-04 22:27:53,999 - INFO - Max workers: 16
2025-08-04 22:27:53,999 - INFO - Connecting to SSH server: 192.168.1.101
2025-08-04 22:27:54,000 - INFO - Connecting to SSH server: 192.168.1.103
2025-08-04 22:27:54,000 - INFO - Connecting to SSH server: 192.168.1.104
2025-08-04 22:27:54,000 - INFO - Scanning local directory: /home/<USER>/
2025-08-04 22:27:54,021 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-04 22:27:54,111 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-04 22:27:56,177 - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-08-04 22:28:05,757 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:28:05,757 - ERROR - Traceback (most recent call last):
2025-08-04 22:28:05,758 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:28:05,758 - ERROR -     handler(m)
2025-08-04 22:28:05,758 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:28:05,758 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:28:05,758 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:28:05,758 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:28:05,758 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:28:05,758 - ERROR - 
2025-08-04 22:28:05,897 - ERROR - Error connecting to SSH server 192.168.1.104: No existing session
2025-08-04 22:28:05,897 - INFO - Found 0 tutorial files on 192.168.1.104
2025-08-04 22:28:06,500 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:28:06,500 - ERROR - Traceback (most recent call last):
2025-08-04 22:28:06,500 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:28:06,500 - ERROR -     handler(m)
2025-08-04 22:28:06,500 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:28:06,500 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:28:06,500 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:28:06,500 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:28:06,501 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:28:06,501 - ERROR - 
2025-08-04 22:28:06,639 - ERROR - Error connecting to SSH server 192.168.1.103: No existing session
2025-08-04 22:28:06,639 - INFO - Found 0 tutorial files on 192.168.1.103
2025-08-04 22:28:07,092 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:28:07,093 - ERROR - Traceback (most recent call last):
2025-08-04 22:28:07,093 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:28:07,093 - ERROR -     handler(m)
2025-08-04 22:28:07,093 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:28:07,093 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:28:07,093 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:28:07,093 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:28:07,093 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:28:07,093 - ERROR - 
2025-08-04 22:28:07,231 - ERROR - Error connecting to SSH server 192.168.1.101: No existing session
2025-08-04 22:28:07,231 - INFO - Found 0 tutorial files on 192.168.1.101
2025-08-04 22:29:44,791 - INFO - Generated unique output filename: tutorial_inventory_20250804_222944_mint-Saints_664baef4.csv
2025-08-04 22:29:44,791 - INFO - Starting tutorial file scan with parallel processing
2025-08-04 22:29:44,791 - INFO - Using parallel processing: True
2025-08-04 22:29:44,791 - INFO - Max workers: 16
2025-08-04 22:29:44,791 - INFO - Connecting to SSH server: 192.168.1.101
2025-08-04 22:29:44,791 - INFO - Connecting to SSH server: 192.168.1.103
2025-08-04 22:29:44,792 - INFO - Connecting to SSH server: 192.168.1.104
2025-08-04 22:29:44,792 - INFO - Scanning local directory: /home/<USER>/
2025-08-04 22:29:44,809 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-04 22:29:44,817 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-04 22:29:45,061 - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-08-04 22:29:52,217 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:29:52,218 - ERROR - Traceback (most recent call last):
2025-08-04 22:29:52,218 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:29:52,218 - ERROR -     handler(m)
2025-08-04 22:29:52,218 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:29:52,218 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:29:52,218 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:29:52,218 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:29:52,218 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:29:52,218 - ERROR - 
2025-08-04 22:29:52,357 - ERROR - Error connecting to SSH server 192.168.1.104: No existing session
2025-08-04 22:29:52,357 - INFO - Found 0 tutorial files on 192.168.1.104
2025-08-04 22:29:53,216 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:29:53,217 - ERROR - Traceback (most recent call last):
2025-08-04 22:29:53,217 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:29:53,217 - ERROR -     handler(m)
2025-08-04 22:29:53,217 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:29:53,217 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:29:53,217 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:29:53,217 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:29:53,217 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:29:53,217 - ERROR - 
2025-08-04 22:29:53,357 - ERROR - Error connecting to SSH server 192.168.1.103: No existing session
2025-08-04 22:29:53,357 - INFO - Found 0 tutorial files on 192.168.1.103
2025-08-04 22:29:54,080 - ERROR - Exception (client): key cannot be used for signing
2025-08-04 22:29:54,080 - ERROR - Traceback (most recent call last):
2025-08-04 22:29:54,080 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/transport.py", line 2262, in run
2025-08-04 22:29:54,080 - ERROR -     handler(m)
2025-08-04 22:29:54,080 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/auth_handler.py", line 404, in _parse_service_accept
2025-08-04 22:29:54,080 - ERROR -     sig = self.private_key.sign_ssh_data(blob, algorithm)
2025-08-04 22:29:54,080 - ERROR -   File "/home/<USER>/anaconda3/envs/flakev/lib/python3.10/site-packages/paramiko/agent.py", line 496, in sign_ssh_data
2025-08-04 22:29:54,080 - ERROR -     raise SSHException("key cannot be used for signing")
2025-08-04 22:29:54,080 - ERROR - paramiko.ssh_exception.SSHException: key cannot be used for signing
2025-08-04 22:29:54,081 - ERROR - 
2025-08-04 22:29:54,219 - ERROR - Error connecting to SSH server 192.168.1.101: No existing session
2025-08-04 22:29:54,219 - INFO - Found 0 tutorial files on 192.168.1.101
2025-08-04 22:42:56,345 - INFO - Found 44376 files to process in /home/<USER>/
2025-08-04 22:42:56,345 - INFO - Processing 44376 files with 16 workers
2025-08-04 22:43:05,741 - INFO - Found 44376 tutorial files in /home/<USER>/
2025-08-04 22:43:05,743 - INFO - Scanning local directory: /media/
2025-08-05 03:42:49,096 - INFO - Found 146645 files to process in /media/
2025-08-05 03:42:49,096 - INFO - Processing 146645 files with 16 workers
2025-08-05 09:09:40,661 - INFO - Generated unique output filename: tutorial_inventory2_20250805_090940_mint-Saints_14c1536f.csv
2025-08-05 09:09:40,661 - INFO - Starting tutorial file scan with parallel processing
2025-08-05 09:09:40,661 - INFO - Using parallel processing: True
2025-08-05 09:09:40,661 - INFO - Max workers: 16
2025-08-05 09:09:40,662 - INFO - Connecting to SSH server: 192.168.1.101
2025-08-05 09:09:40,662 - INFO - Connecting to SSH server: 192.168.1.103
2025-08-05 09:09:40,662 - INFO - Connecting to SSH server: 192.168.1.104
2025-08-05 09:09:40,662 - INFO - Scanning local directory: /home/<USER>/
2025-08-05 09:09:40,723 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-05 09:09:40,724 - INFO - Connected (version 2.0, client OpenSSH_9.6p1)
2025-08-05 09:09:41,011 - INFO - Authentication (password) successful!
2025-08-05 09:09:41,017 - INFO - Authentication (password) successful!
2025-08-05 09:09:41,057 - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-08-05 09:09:41,102 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-08-05 09:09:41,102 - INFO - Scanning remote directory: 192.168.1.104:/home/<USER>
2025-08-05 09:09:41,117 - INFO - No tutorial files found in 192.168.1.104:/home/<USER>
2025-08-05 09:09:41,118 - INFO - Scanning remote directory: 192.168.1.104:/media/
2025-08-05 09:09:41,217 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-08-05 09:09:41,217 - INFO - Scanning remote directory: 192.168.1.103:/home/<USER>
2025-08-05 09:09:41,864 - INFO - Authentication (password) successful!
2025-08-05 09:09:42,039 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-08-05 09:09:42,039 - INFO - Scanning remote directory: 192.168.1.101:/home/<USER>
2025-08-05 09:17:55,797 - INFO - Found 279 files to process in /home/<USER>/
2025-08-05 09:17:55,797 - INFO - Processing 279 files with 16 workers
2025-08-05 09:18:00,375 - INFO - Found 279 tutorial files in /home/<USER>/
2025-08-05 09:18:00,375 - INFO - Scanning local directory: /media/
2025-08-05 10:35:18,049 - INFO - Scanning remote directory: 192.168.1.103:/media/
2025-08-05 10:35:18,069 - INFO - No tutorial files found in 192.168.1.103:/media/
2025-08-05 10:35:18,069 - INFO - [chan 0] sftp session closed.
2025-08-05 10:35:18,069 - INFO - Found 274 tutorial files on 192.168.1.103
2025-08-05 12:03:23,454 - INFO - Found 21317 files to process in /media/
2025-08-05 12:03:23,454 - INFO - Processing 21317 files with 16 workers
