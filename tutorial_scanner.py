#!/usr/bin/env python3
"""
Tutorial File Scanner

A comprehensive script to scan for tutorial files across multiple SSH servers and local drives.
Generates detailed inventory reports with duplicate detection and metadata extraction.
"""

import os
import csv
import json
import hashlib
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from collections import defaultdict
import mimetypes
import stat
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import threading
import time

# SSH and remote file handling
try:
    import paramiko
    from paramiko import SSHClient, AutoAddPolicy
    SSH_AVAILABLE = True
except ImportError:
    SSH_AVAILABLE = False
    print("Warning: paramiko not installed. SSH functionality will be disabled.")
    print("Install with: pip install paramiko")

# Progress tracking
try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False


def process_local_file(args):
    """
    Process a single local file - designed for multiprocessing.

    Args:
        args: Tuple of (file_path, config_dict)

    Returns:
        Optional[Dict[str, Any]]: File information dictionary or None
    """
    file_path, config = args

    try:
        # Recreate necessary objects from config
        hash_algo = config['hash_algorithm']
        chunk_size = config['chunk_size']
        max_size = config['max_file_size_mb'] * 1024 * 1024

        # Get file stats
        stat_info = os.stat(file_path)
        file_size = stat_info.st_size
        mod_time = datetime.fromtimestamp(stat_info.st_mtime)
        filename = os.path.basename(file_path)

        # Calculate hash if file is not too large
        if file_size > max_size:
            file_hash = "SKIPPED_TOO_LARGE"
        else:
            try:
                hash_obj = hashlib.new(hash_algo)
                with open(file_path, 'rb') as f:
                    while chunk := f.read(chunk_size):
                        hash_obj.update(chunk)
                file_hash = hash_obj.hexdigest()
            except Exception:
                file_hash = "ERROR"

        # Detect content type
        content_type, _ = mimetypes.guess_type(filename)
        if not content_type:
            content_type = "unknown"

        # Get file extension
        file_ext = Path(filename).suffix.lower()

        # Format file size
        size_mb = round(file_size / (1024 * 1024), 2)
        size_human = format_file_size(file_size)

        return {
            'filename': filename,
            'full_path': file_path,
            'source': "local",
            'file_size_bytes': file_size,
            'file_size_mb': size_mb,
            'file_size_human': size_human,
            'file_extension': file_ext,
            'content_type': content_type,
            'modification_date': mod_time.strftime('%Y-%m-%d %H:%M:%S'),
            'checksum': file_hash,
            'hash_algorithm': hash_algo
        }

    except Exception as e:
        return None


def format_file_size(size_bytes: int) -> str:
    """Format file size in human-readable format."""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} PB"


class TutorialScanner:
    """Main scanner class for tutorial files across multiple locations."""
    
    def __init__(self, config_file: str = None):
        """
        Initialize the scanner with configuration.
        
        Args:
            config_file (str): Path to JSON configuration file
        """
        self.config = self.load_config(config_file)
        self.setup_logging()
        self.files_found = []
        self.errors = []
        self.stats = defaultdict(lambda: {'files': 0, 'size': 0, 'errors': 0})
        
    def load_config(self, config_file: str = None) -> Dict[str, Any]:
        """Load configuration from file or use defaults."""
        default_config = {
            "ssh_servers": [],
            "local_directories": ["."],
            "file_extensions": [".pdf", ".mp4", ".mkv", ".avi", ".txt", ".md", ".docx", ".pptx", ".epub"],
            "output_file": "tutorial_inventory.csv",
            "separator": "|~|",
            "hash_algorithm": "md5",
            "max_file_size_mb": 10000,  # Skip files larger than 10GB for hashing
            "ssh_timeout": 30,
            "chunk_size": 8192,
            "max_workers": None,  # Auto-detect CPU cores
            "parallel_processing": True,
            "use_threading_for_ssh": True  # Use threading for SSH to avoid pickling issues
        }
        
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
                self.log(f"Loaded configuration from {config_file}")
            except Exception as e:
                self.log(f"Error loading config file: {e}", level="error")
        
        return default_config
    
    def setup_logging(self):
        """Setup logging configuration."""
        log_level = getattr(logging, self.config.get('log_level', 'INFO').upper())
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('tutorial_scanner.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def log(self, message: str, level: str = "info"):
        """Log a message."""
        getattr(self.logger, level)(message)
    
    def calculate_hash(self, file_path: str, is_remote: bool = False, sftp_client=None) -> str:
        """
        Calculate file hash for duplicate detection.
        
        Args:
            file_path (str): Path to the file
            is_remote (bool): Whether the file is on a remote server
            sftp_client: SFTP client for remote files
            
        Returns:
            str: File hash or "ERROR" if calculation fails
        """
        hash_algo = self.config['hash_algorithm']
        chunk_size = self.config['chunk_size']
        
        try:
            hash_obj = hashlib.new(hash_algo)
            
            if is_remote and sftp_client:
                with sftp_client.open(file_path, 'rb') as f:
                    while chunk := f.read(chunk_size):
                        hash_obj.update(chunk)
            else:
                with open(file_path, 'rb') as f:
                    while chunk := f.read(chunk_size):
                        hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            self.log(f"Error calculating hash for {file_path}: {e}", level="warning")
            return "ERROR"
    
    def get_file_info(self, file_path: str, source: str, is_remote: bool = False, 
                     sftp_client=None, ssh_client=None) -> Optional[Dict[str, Any]]:
        """
        Extract comprehensive file information.
        
        Args:
            file_path (str): Path to the file
            source (str): Source identifier (hostname or "local")
            is_remote (bool): Whether the file is on a remote server
            sftp_client: SFTP client for remote files
            ssh_client: SSH client for remote commands
            
        Returns:
            Optional[Dict[str, Any]]: File information dictionary
        """
        try:
            if is_remote and sftp_client:
                # Get remote file stats
                stat_info = sftp_client.stat(file_path)
                file_size = stat_info.st_size
                mod_time = datetime.fromtimestamp(stat_info.st_mtime)
                
                # Get filename
                filename = os.path.basename(file_path)
                
            else:
                # Get local file stats
                stat_info = os.stat(file_path)
                file_size = stat_info.st_size
                mod_time = datetime.fromtimestamp(stat_info.st_mtime)
                filename = os.path.basename(file_path)
            
            # Skip very large files for hashing
            max_size = self.config['max_file_size_mb'] * 1024 * 1024
            if file_size > max_size:
                file_hash = "SKIPPED_TOO_LARGE"
                self.log(f"Skipping hash for large file: {file_path} ({file_size / (1024*1024):.1f} MB)")
            else:
                file_hash = self.calculate_hash(file_path, is_remote, sftp_client)
            
            # Detect content type
            content_type, _ = mimetypes.guess_type(filename)
            if not content_type:
                content_type = "unknown"
            
            # Get file extension
            file_ext = Path(filename).suffix.lower()
            
            # Format file size
            size_mb = round(file_size / (1024 * 1024), 2)
            size_human = self.format_file_size(file_size)
            
            return {
                'filename': filename,
                'full_path': file_path,
                'source': source,
                'file_size_bytes': file_size,
                'file_size_mb': size_mb,
                'file_size_human': size_human,
                'file_extension': file_ext,
                'content_type': content_type,
                'modification_date': mod_time.strftime('%Y-%m-%d %H:%M:%S'),
                'checksum': file_hash,
                'hash_algorithm': self.config['hash_algorithm']
            }
            
        except Exception as e:
            self.log(f"Error getting file info for {file_path}: {e}", level="warning")
            self.errors.append(f"File info error: {file_path} - {str(e)}")
            return None
    
    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format."""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def scan_local_directory(self, directory: str) -> List[Dict[str, Any]]:
        """
        Scan a local directory for tutorial files using parallel processing.

        Args:
            directory (str): Directory path to scan

        Returns:
            List[Dict[str, Any]]: List of file information dictionaries
        """
        files_found = []
        extensions = tuple(self.config['file_extensions'])

        self.log(f"Scanning local directory: {directory}")

        try:
            directory_path = Path(directory)
            if not directory_path.exists():
                self.log(f"Directory does not exist: {directory}", level="error")
                self.errors.append(f"Directory not found: {directory}")
                return files_found

            # Get all files recursively
            all_files = []
            for ext in extensions:
                pattern = f"**/*{ext}"
                all_files.extend([str(f) for f in directory_path.rglob(pattern) if f.is_file()])

            if not all_files:
                self.log(f"No tutorial files found in {directory}")
                return files_found

            self.log(f"Found {len(all_files)} files to process in {directory}")

            # Use parallel processing if enabled and beneficial
            if self.config.get('parallel_processing', True) and len(all_files) > 10:
                files_found = self._process_files_parallel(all_files, "local")
            else:
                files_found = self._process_files_sequential(all_files, "local")

            # Update statistics
            for file_info in files_found:
                self.stats["local"]['files'] += 1
                self.stats["local"]['size'] += file_info['file_size_bytes']

        except Exception as e:
            self.log(f"Error scanning local directory {directory}: {e}", level="error")
            self.errors.append(f"Local scan error: {directory} - {str(e)}")
            self.stats["local"]['errors'] += 1

        self.log(f"Found {len(files_found)} tutorial files in {directory}")
        return files_found

    def _process_files_parallel(self, file_paths: List[str], source: str) -> List[Dict[str, Any]]:
        """Process files in parallel using multiprocessing."""
        files_found = []
        max_workers = self.config.get('max_workers') or mp.cpu_count()

        self.log(f"Processing {len(file_paths)} files with {max_workers} workers")

        # Prepare arguments for parallel processing
        process_args = [(file_path, self.config) for file_path in file_paths]

        try:
            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                # Submit all tasks
                if TQDM_AVAILABLE:
                    futures = {executor.submit(process_local_file, args): args[0]
                              for args in process_args}

                    # Process with progress bar
                    for future in tqdm(as_completed(futures), total=len(futures),
                                     desc=f"Processing {source} files"):
                        try:
                            result = future.result()
                            if result:
                                result['source'] = source  # Ensure correct source
                                files_found.append(result)
                        except Exception as e:
                            file_path = futures[future]
                            self.log(f"Error processing {file_path}: {e}", level="warning")
                            self.errors.append(f"File processing error: {file_path} - {str(e)}")
                else:
                    # Process without progress bar
                    futures = {executor.submit(process_local_file, args): args[0]
                              for args in process_args}

                    for future in as_completed(futures):
                        try:
                            result = future.result()
                            if result:
                                result['source'] = source  # Ensure correct source
                                files_found.append(result)
                        except Exception as e:
                            file_path = futures[future]
                            self.log(f"Error processing {file_path}: {e}", level="warning")
                            self.errors.append(f"File processing error: {file_path} - {str(e)}")

        except Exception as e:
            self.log(f"Error in parallel processing: {e}", level="error")
            # Fallback to sequential processing
            self.log("Falling back to sequential processing", level="warning")
            files_found = self._process_files_sequential(file_paths, source)

        return files_found

    def _process_files_sequential(self, file_paths: List[str], source: str) -> List[Dict[str, Any]]:
        """Process files sequentially (fallback method)."""
        files_found = []

        if TQDM_AVAILABLE:
            file_iterator = tqdm(file_paths, desc=f"Processing {source} files")
        else:
            file_iterator = file_paths

        for file_path in file_iterator:
            try:
                file_info = self.get_file_info(file_path, source)
                if file_info:
                    files_found.append(file_info)
            except Exception as e:
                self.log(f"Error processing {file_path}: {e}", level="warning")
                self.errors.append(f"File processing error: {file_path} - {str(e)}")

        return files_found

    def scan_ssh_server(self, server_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Scan an SSH server for tutorial files.

        Args:
            server_config (Dict[str, Any]): SSH server configuration

        Returns:
            List[Dict[str, Any]]: List of file information dictionaries
        """
        if not SSH_AVAILABLE:
            self.log("SSH functionality not available. Install paramiko.", level="error")
            return []

        files_found = []
        hostname = server_config.get('hostname', 'unknown')

        try:
            # Create SSH client
            ssh_client = SSHClient()
            ssh_client.set_missing_host_key_policy(AutoAddPolicy())

            # Connection parameters
            connect_params = {
                'hostname': hostname,
                'port': server_config.get('port', 22),
                'username': server_config.get('username'),
                'timeout': self.config['ssh_timeout']
            }

            # Add authentication method
            if 'key_file' in server_config:
                connect_params['key_filename'] = server_config['key_file']
            elif 'password' in server_config:
                connect_params['password'] = server_config['password']

            self.log(f"Connecting to SSH server: {hostname}")
            ssh_client.connect(**connect_params)

            # Create SFTP client
            sftp_client = ssh_client.open_sftp()

            # Scan each directory
            directories = server_config.get('directories', ['/'])
            for directory in directories:
                self.log(f"Scanning remote directory: {hostname}:{directory}")
                dir_files = self.scan_remote_directory(
                    sftp_client, ssh_client, directory, hostname
                )
                files_found.extend(dir_files)

            # Close connections
            sftp_client.close()
            ssh_client.close()

        except Exception as e:
            self.log(f"Error connecting to SSH server {hostname}: {e}", level="error")
            self.errors.append(f"SSH connection error: {hostname} - {str(e)}")
            self.stats[hostname]['errors'] += 1

        self.log(f"Found {len(files_found)} tutorial files on {hostname}")
        return files_found

    def scan_remote_directory(self, sftp_client, ssh_client, directory: str,
                            hostname: str) -> List[Dict[str, Any]]:
        """
        Scan a remote directory via SFTP.

        Args:
            sftp_client: SFTP client
            ssh_client: SSH client
            directory (str): Remote directory path
            hostname (str): Server hostname

        Returns:
            List[Dict[str, Any]]: List of file information dictionaries
        """
        files_found = []
        extensions = self.config['file_extensions']

        try:
            # Build find command for efficiency
            ext_patterns = " -o ".join([f'-name "*.{ext.lstrip(".")}"' for ext in extensions])
            find_command = f'find "{directory}" -type f \\( {ext_patterns} \\) 2>/dev/null'

            # Execute find command
            stdin, stdout, stderr = ssh_client.exec_command(find_command)
            file_paths = stdout.read().decode('utf-8').strip().split('\n')

            # Filter out empty lines
            file_paths = [path.strip() for path in file_paths if path.strip()]

            if not file_paths:
                self.log(f"No tutorial files found in {hostname}:{directory}")
                return files_found

            # Process files with progress bar if available
            if TQDM_AVAILABLE:
                file_iterator = tqdm(file_paths, desc=f"Processing {hostname}:{directory}")
            else:
                file_iterator = file_paths

            for file_path in file_iterator:
                try:
                    file_info = self.get_file_info(
                        file_path, hostname, is_remote=True,
                        sftp_client=sftp_client, ssh_client=ssh_client
                    )
                    if file_info:
                        files_found.append(file_info)
                        self.stats[hostname]['files'] += 1
                        self.stats[hostname]['size'] += file_info['file_size_bytes']

                except Exception as e:
                    self.log(f"Error processing remote file {file_path}: {e}", level="warning")
                    self.errors.append(f"Remote file error: {hostname}:{file_path} - {str(e)}")
                    self.stats[hostname]['errors'] += 1

        except Exception as e:
            self.log(f"Error scanning remote directory {hostname}:{directory}: {e}", level="error")
            self.errors.append(f"Remote directory error: {hostname}:{directory} - {str(e)}")
            self.stats[hostname]['errors'] += 1

        return files_found

    def find_duplicates(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Find duplicate files based on checksum and size.

        Returns:
            Dict[str, List[Dict[str, Any]]]: Dictionary mapping checksums to file lists
        """
        checksum_map = defaultdict(list)

        for file_info in self.files_found:
            checksum = file_info['checksum']
            if checksum != "ERROR" and checksum != "SKIPPED_TOO_LARGE":
                checksum_map[checksum].append(file_info)

        # Filter to only include actual duplicates
        duplicates = {k: v for k, v in checksum_map.items() if len(v) > 1}

        self.log(f"Found {len(duplicates)} groups of duplicate files")
        return duplicates

    def write_csv_report(self, output_file: str = None) -> None:
        """
        Write comprehensive CSV report.

        Args:
            output_file (str): Output file path
        """
        if output_file is None:
            output_file = self.config['output_file']

        separator = self.config['separator']

        try:
            fieldnames = [
                'filename', 'full_path', 'source', 'file_size_bytes', 'file_size_mb',
                'file_size_human', 'file_extension', 'content_type', 'modification_date',
                'checksum', 'hash_algorithm', 'is_duplicate'
            ]

            # Find duplicates
            duplicates = self.find_duplicates()
            duplicate_checksums = set(duplicates.keys())

            # Mark duplicates
            for file_info in self.files_found:
                file_info['is_duplicate'] = file_info['checksum'] in duplicate_checksums

            # Write CSV with custom separator handling
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                if len(separator) == 1:
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames, delimiter=separator)
                    writer.writeheader()
                    writer.writerows(self.files_found)
                else:
                    # Handle multi-character separators
                    csvfile.write(separator.join(fieldnames) + '\n')
                    for file_info in self.files_found:
                        row_values = []
                        for field in fieldnames:
                            value = str(file_info.get(field, ''))
                            if separator in value:
                                value = value.replace(separator, f'\\{separator}')
                            row_values.append(value)
                        csvfile.write(separator.join(row_values) + '\n')

            self.log(f"CSV report written to: {output_file}")

            # Write duplicates report
            if duplicates:
                dup_file = output_file.replace('.csv', '_duplicates.csv')
                self.write_duplicates_report(duplicates, dup_file)

        except Exception as e:
            self.log(f"Error writing CSV report: {e}", level="error")

    def write_duplicates_report(self, duplicates: Dict[str, List[Dict[str, Any]]],
                              output_file: str) -> None:
        """Write detailed duplicates report."""
        separator = self.config['separator']

        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['checksum', 'duplicate_count', 'total_size_mb', 'file_details']

                if len(separator) == 1:
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames, delimiter=separator)
                    writer.writeheader()

                    for checksum, file_list in duplicates.items():
                        total_size = sum(f['file_size_bytes'] for f in file_list)
                        file_details = ' ¦¦¦ '.join([
                            f"{f['source']}:{f['full_path']} ({f['file_size_human']})"
                            for f in file_list
                        ])

                        writer.writerow({
                            'checksum': checksum,
                            'duplicate_count': len(file_list),
                            'total_size_mb': round(total_size / (1024*1024), 2),
                            'file_details': file_details
                        })
                else:
                    # Handle multi-character separators
                    csvfile.write(separator.join(fieldnames) + '\n')

                    for checksum, file_list in duplicates.items():
                        total_size = sum(f['file_size_bytes'] for f in file_list)
                        file_details = ' ¦¦¦ '.join([
                            f"{f['source']}:{f['full_path']} ({f['file_size_human']})"
                            for f in file_list
                        ])

                        # Escape separator in data
                        if separator in file_details:
                            file_details = file_details.replace(separator, f'\\{separator}')

                        row_values = [
                            checksum,
                            str(len(file_list)),
                            str(round(total_size / (1024*1024), 2)),
                            file_details
                        ]
                        csvfile.write(separator.join(row_values) + '\n')

            self.log(f"Duplicates report written to: {output_file}")

        except Exception as e:
            self.log(f"Error writing duplicates report: {e}", level="error")

    def print_summary(self) -> None:
        """Print scan summary statistics."""
        total_files = len(self.files_found)
        total_size = sum(f['file_size_bytes'] for f in self.files_found)
        duplicates = self.find_duplicates()

        print("\n" + "="*60)
        print("TUTORIAL FILE SCAN SUMMARY")
        print("="*60)
        print(f"Total files found: {total_files}")
        print(f"Total size: {self.format_file_size(total_size)}")
        print(f"Duplicate groups: {len(duplicates)}")

        if duplicates:
            dup_files = sum(len(files) for files in duplicates.values())
            dup_size = sum(
                sum(f['file_size_bytes'] for f in files)
                for files in duplicates.values()
            )
            print(f"Duplicate files: {dup_files}")
            print(f"Wasted space: {self.format_file_size(dup_size)}")

        print(f"Errors encountered: {len(self.errors)}")

        print("\nPer-source statistics:")
        for source, stats in self.stats.items():
            print(f"  {source}:")
            print(f"    Files: {stats['files']}")
            print(f"    Size: {self.format_file_size(stats['size'])}")
            print(f"    Errors: {stats['errors']}")

        if self.errors:
            print(f"\nFirst 5 errors:")
            for error in self.errors[:5]:
                print(f"  - {error}")
            if len(self.errors) > 5:
                print(f"  ... and {len(self.errors) - 5} more errors")

    def scan_all(self) -> None:
        """Perform complete scan of all configured locations using parallel processing."""
        self.log("Starting tutorial file scan with parallel processing")
        start_time = datetime.now()

        # Determine if we should use parallel processing
        use_parallel = self.config.get('parallel_processing', True)
        max_workers = self.config.get('max_workers') or mp.cpu_count()

        self.log(f"Using parallel processing: {use_parallel}")
        if use_parallel:
            self.log(f"Max workers: {max_workers}")

        all_results = []

        if use_parallel and (len(self.config['local_directories']) > 1 or len(self.config['ssh_servers']) > 1):
            # Use parallel processing for multiple locations
            all_results = self._scan_all_parallel()
        else:
            # Use sequential processing
            all_results = self._scan_all_sequential()

        # Consolidate all results into single list
        for result_batch in all_results:
            self.files_found.extend(result_batch)

        end_time = datetime.now()
        duration = end_time - start_time

        self.log(f"Scan completed in {duration}")
        self.log(f"Total files found: {len(self.files_found)}")

        # Generate single consolidated report
        self.write_csv_report()
        self.print_summary()

    def _scan_all_parallel(self) -> List[List[Dict[str, Any]]]:
        """Scan all locations in parallel using threading for SSH and multiprocessing for local."""
        all_results = []

        # Use ThreadPoolExecutor for SSH servers (to avoid pickling SSH connections)
        # and ProcessPoolExecutor for local directories (for CPU-intensive hashing)

        with ThreadPoolExecutor(max_workers=len(self.config['ssh_servers']) + 1) as thread_executor:
            futures = []

            # Submit SSH server scanning tasks
            for server_config in self.config['ssh_servers']:
                future = thread_executor.submit(self.scan_ssh_server, server_config)
                futures.append(('ssh', server_config.get('hostname', 'unknown'), future))

            # Submit local directory scanning task (this will use multiprocessing internally)
            if self.config['local_directories']:
                future = thread_executor.submit(self._scan_all_local_directories)
                futures.append(('local', 'local', future))

            # Collect results with progress tracking
            if TQDM_AVAILABLE:
                future_iterator = tqdm(futures, desc="Scanning locations")
            else:
                future_iterator = futures

            for scan_type, location, future in future_iterator:
                try:
                    result = future.result()
                    if result:
                        all_results.append(result)
                        self.log(f"Completed {scan_type} scan for {location}: {len(result)} files")
                except Exception as e:
                    self.log(f"Error scanning {scan_type} location {location}: {e}", level="error")
                    self.errors.append(f"{scan_type} scan error: {location} - {str(e)}")

        return all_results

    def _scan_all_sequential(self) -> List[List[Dict[str, Any]]]:
        """Scan all locations sequentially."""
        all_results = []

        # Scan local directories
        local_files = self._scan_all_local_directories()
        if local_files:
            all_results.append(local_files)

        # Scan SSH servers
        for server_config in self.config['ssh_servers']:
            ssh_files = self.scan_ssh_server(server_config)
            if ssh_files:
                all_results.append(ssh_files)

        return all_results

    def _scan_all_local_directories(self) -> List[Dict[str, Any]]:
        """Scan all local directories and return combined results."""
        all_local_files = []

        for directory in self.config['local_directories']:
            local_files = self.scan_local_directory(directory)
            all_local_files.extend(local_files)

        return all_local_files


def create_sample_config(config_file: str = "tutorial_scanner_config.json") -> None:
    """Create a sample configuration file."""
    sample_config = {
        "ssh_servers": [
            {
                "hostname": "*************",
                "username": "admin",
                "password": "your_password",
                "directories": [
                    "/home/<USER>",
                    "/media/"
                ]
            },
                        {
                "hostname": "*************",
                "username": "admin",
                "password": "your_password",
                "directories": [
                    "/home/<USER>",
                    "/media/"
                ]
            },

            {
                "hostname": "*************",
                "username": "ubuntu",
                "password": "ubuntu",
                "directories": [
                    "/home/<USER>",
                    "/media/"
                ]
            }
        ],
        "local_directories": [
            "/home/<USER>/",
            "/media/",
        ],
        "file_extensions": [
            ".pdf", ".mp4", ".mkv", ".avi", ".mov", ".wmv",
            ".txt", ".md", ".rst", ".docx", ".pptx", ".epub",
            ".zip", ".rar", ".7z"
        ],
        "output_file": "tutorial_inventory.csv",
        "separator": "|~|",
        "hash_algorithm": "md5",
        "max_file_size_mb": 10000,
        "ssh_timeout": 30,
        "chunk_size": 8192,
        "log_level": "INFO"
    }

    try:
        with open(config_file, 'w') as f:
            json.dump(sample_config, f, indent=2)
        print(f"Sample configuration created: {config_file}")
        print("Please edit this file with your actual server details and paths.")
    except Exception as e:
        print(f"Error creating sample config: {e}")


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="Scan for tutorial files across SSH servers and local drives",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Create sample configuration file
  python tutorial_scanner.py --create-config

  # Run scan with default config
  python tutorial_scanner.py

  # Run scan with custom config
  python tutorial_scanner.py --config my_config.json

  # Run scan with custom output file
  python tutorial_scanner.py --output my_inventory.csv

  # Scan only local directories (no SSH)
  python tutorial_scanner.py --local-only
        """
    )

    parser.add_argument(
        '--config', '-c',
        default='tutorial_scanner_config.json',
        help='Configuration file path (default: tutorial_scanner_config.json)'
    )
    parser.add_argument(
        '--output', '-o',
        help='Output CSV file path (overrides config)'
    )
    parser.add_argument(
        '--create-config',
        action='store_true',
        help='Create a sample configuration file and exit'
    )
    parser.add_argument(
        '--local-only',
        action='store_true',
        help='Scan only local directories (skip SSH servers)'
    )
    parser.add_argument(
        '--separator',
        default='|~|',
        help='CSV separator to use (default: |~|)'
    )
    parser.add_argument(
        '--workers',
        type=int,
        help='Number of worker processes (default: auto-detect CPU cores)'
    )
    parser.add_argument(
        '--no-parallel',
        action='store_true',
        help='Disable parallel processing (use sequential scanning)'
    )
    parser.add_argument(
        '--benchmark',
        action='store_true',
        help='Show performance benchmarks and timing information'
    )

    args = parser.parse_args()

    if args.create_config:
        create_sample_config(args.config)
        return 0

    try:
        # Initialize scanner
        scanner = TutorialScanner(args.config)

        # Override config with command line arguments
        if args.output:
            scanner.config['output_file'] = args.output
        if args.separator:
            scanner.config['separator'] = args.separator
        if args.local_only:
            scanner.config['ssh_servers'] = []
        if args.workers:
            scanner.config['max_workers'] = args.workers
        if args.no_parallel:
            scanner.config['parallel_processing'] = False

        # Show configuration info
        if args.benchmark or scanner.config.get('parallel_processing', True):
            cpu_count = mp.cpu_count()
            max_workers = scanner.config.get('max_workers', cpu_count)
            parallel_enabled = scanner.config.get('parallel_processing', True)

            print(f"System: {cpu_count} CPU cores detected")
            print(f"Parallel processing: {'Enabled' if parallel_enabled else 'Disabled'}")
            if parallel_enabled:
                print(f"Max workers: {max_workers}")
            print("-" * 50)

        # Perform scan with timing
        scan_start = time.time()
        scanner.scan_all()
        scan_end = time.time()

        scan_duration = scan_end - scan_start

        # Show performance information
        if args.benchmark:
            total_files = len(scanner.files_found)
            total_size = sum(f.get('file_size_bytes', 0) for f in scanner.files_found)

            print(f"\n" + "="*50)
            print("PERFORMANCE BENCHMARKS")
            print("="*50)
            print(f"Total scan time: {scan_duration:.2f} seconds")
            print(f"Files processed: {total_files}")
            print(f"Processing rate: {total_files/scan_duration:.1f} files/second")
            print(f"Data processed: {total_size/(1024*1024):.1f} MB")
            print(f"Throughput: {total_size/(1024*1024)/scan_duration:.1f} MB/second")

            parallel_enabled = scanner.config.get('parallel_processing', True)
            if parallel_enabled:
                max_workers = scanner.config.get('max_workers', mp.cpu_count())
                print(f"Parallel processing: Enabled ({max_workers} workers)")
                print(f"Efficiency: {total_files/(scan_duration*max_workers):.1f} files/second/worker")
            else:
                print(f"Parallel processing: Disabled")

        print(f"\nScan complete! Check the following files:")
        print(f"  - Main report: {scanner.config['output_file']}")
        print(f"  - Log file: tutorial_scanner.log")

        duplicates_file = scanner.config['output_file'].replace('.csv', '_duplicates.csv')
        if os.path.exists(duplicates_file):
            print(f"  - Duplicates report: {duplicates_file}")

        if args.benchmark:
            print(f"\nPerformance: {total_files} files in {scan_duration:.1f}s ({total_files/scan_duration:.1f} files/sec)")

        return 0

    except KeyboardInterrupt:
        print("\nScan interrupted by user")
        return 1
    except Exception as e:
        print(f"Error during scan: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
