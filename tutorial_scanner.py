#!/usr/bin/env python3
"""
Tutorial File Scanner

A comprehensive script to scan for tutorial files across multiple SSH servers and local drives.
Generates detailed inventory reports with duplicate detection and metadata extraction.
"""

import os
import csv
import json
import hashlib
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from collections import defaultdict
import mimetypes
import stat

# SSH and remote file handling
try:
    import paramiko
    from paramiko import SSHClient, AutoAddPolicy
    SSH_AVAILABLE = True
except ImportError:
    SSH_AVAILABLE = False
    print("Warning: paramiko not installed. SSH functionality will be disabled.")
    print("Install with: pip install paramiko")

# Progress tracking
try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False


class TutorialScanner:
    """Main scanner class for tutorial files across multiple locations."""
    
    def __init__(self, config_file: str = None):
        """
        Initialize the scanner with configuration.
        
        Args:
            config_file (str): Path to JSON configuration file
        """
        self.config = self.load_config(config_file)
        self.setup_logging()
        self.files_found = []
        self.errors = []
        self.stats = defaultdict(lambda: {'files': 0, 'size': 0, 'errors': 0})
        
    def load_config(self, config_file: str = None) -> Dict[str, Any]:
        """Load configuration from file or use defaults."""
        default_config = {
            "ssh_servers": [],
            "local_directories": ["."],
            "file_extensions": [".pdf", ".mp4", ".mkv", ".avi", ".txt", ".md", ".docx", ".pptx", ".epub"],
            "output_file": "tutorial_inventory.csv",
            "separator": "|~|",
            "hash_algorithm": "md5",
            "max_file_size_mb": 10000,  # Skip files larger than 10GB for hashing
            "ssh_timeout": 30,
            "chunk_size": 8192
        }
        
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
                self.log(f"Loaded configuration from {config_file}")
            except Exception as e:
                self.log(f"Error loading config file: {e}", level="error")
        
        return default_config
    
    def setup_logging(self):
        """Setup logging configuration."""
        log_level = getattr(logging, self.config.get('log_level', 'INFO').upper())
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('tutorial_scanner.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def log(self, message: str, level: str = "info"):
        """Log a message."""
        getattr(self.logger, level)(message)
    
    def calculate_hash(self, file_path: str, is_remote: bool = False, sftp_client=None) -> str:
        """
        Calculate file hash for duplicate detection.
        
        Args:
            file_path (str): Path to the file
            is_remote (bool): Whether the file is on a remote server
            sftp_client: SFTP client for remote files
            
        Returns:
            str: File hash or "ERROR" if calculation fails
        """
        hash_algo = self.config['hash_algorithm']
        chunk_size = self.config['chunk_size']
        
        try:
            hash_obj = hashlib.new(hash_algo)
            
            if is_remote and sftp_client:
                with sftp_client.open(file_path, 'rb') as f:
                    while chunk := f.read(chunk_size):
                        hash_obj.update(chunk)
            else:
                with open(file_path, 'rb') as f:
                    while chunk := f.read(chunk_size):
                        hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            self.log(f"Error calculating hash for {file_path}: {e}", level="warning")
            return "ERROR"
    
    def get_file_info(self, file_path: str, source: str, is_remote: bool = False, 
                     sftp_client=None, ssh_client=None) -> Optional[Dict[str, Any]]:
        """
        Extract comprehensive file information.
        
        Args:
            file_path (str): Path to the file
            source (str): Source identifier (hostname or "local")
            is_remote (bool): Whether the file is on a remote server
            sftp_client: SFTP client for remote files
            ssh_client: SSH client for remote commands
            
        Returns:
            Optional[Dict[str, Any]]: File information dictionary
        """
        try:
            if is_remote and sftp_client:
                # Get remote file stats
                stat_info = sftp_client.stat(file_path)
                file_size = stat_info.st_size
                mod_time = datetime.fromtimestamp(stat_info.st_mtime)
                
                # Get filename
                filename = os.path.basename(file_path)
                
            else:
                # Get local file stats
                stat_info = os.stat(file_path)
                file_size = stat_info.st_size
                mod_time = datetime.fromtimestamp(stat_info.st_mtime)
                filename = os.path.basename(file_path)
            
            # Skip very large files for hashing
            max_size = self.config['max_file_size_mb'] * 1024 * 1024
            if file_size > max_size:
                file_hash = "SKIPPED_TOO_LARGE"
                self.log(f"Skipping hash for large file: {file_path} ({file_size / (1024*1024):.1f} MB)")
            else:
                file_hash = self.calculate_hash(file_path, is_remote, sftp_client)
            
            # Detect content type
            content_type, _ = mimetypes.guess_type(filename)
            if not content_type:
                content_type = "unknown"
            
            # Get file extension
            file_ext = Path(filename).suffix.lower()
            
            # Format file size
            size_mb = round(file_size / (1024 * 1024), 2)
            size_human = self.format_file_size(file_size)
            
            return {
                'filename': filename,
                'full_path': file_path,
                'source': source,
                'file_size_bytes': file_size,
                'file_size_mb': size_mb,
                'file_size_human': size_human,
                'file_extension': file_ext,
                'content_type': content_type,
                'modification_date': mod_time.strftime('%Y-%m-%d %H:%M:%S'),
                'checksum': file_hash,
                'hash_algorithm': self.config['hash_algorithm']
            }
            
        except Exception as e:
            self.log(f"Error getting file info for {file_path}: {e}", level="warning")
            self.errors.append(f"File info error: {file_path} - {str(e)}")
            return None
    
    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format."""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def scan_local_directory(self, directory: str) -> List[Dict[str, Any]]:
        """
        Scan a local directory for tutorial files.
        
        Args:
            directory (str): Directory path to scan
            
        Returns:
            List[Dict[str, Any]]: List of file information dictionaries
        """
        files_found = []
        extensions = tuple(self.config['file_extensions'])
        
        self.log(f"Scanning local directory: {directory}")
        
        try:
            directory_path = Path(directory)
            if not directory_path.exists():
                self.log(f"Directory does not exist: {directory}", level="error")
                self.errors.append(f"Directory not found: {directory}")
                return files_found
            
            # Get all files recursively
            all_files = []
            for ext in extensions:
                pattern = f"**/*{ext}"
                all_files.extend(directory_path.rglob(pattern))
            
            # Process files with progress bar if available
            if TQDM_AVAILABLE:
                file_iterator = tqdm(all_files, desc=f"Scanning {directory}")
            else:
                file_iterator = all_files
            
            for file_path in file_iterator:
                if file_path.is_file():
                    file_info = self.get_file_info(str(file_path), "local")
                    if file_info:
                        files_found.append(file_info)
                        self.stats["local"]['files'] += 1
                        self.stats["local"]['size'] += file_info['file_size_bytes']
            
        except Exception as e:
            self.log(f"Error scanning local directory {directory}: {e}", level="error")
            self.errors.append(f"Local scan error: {directory} - {str(e)}")
            self.stats["local"]['errors'] += 1
        
        self.log(f"Found {len(files_found)} tutorial files in {directory}")
        return files_found

    def scan_ssh_server(self, server_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Scan an SSH server for tutorial files.

        Args:
            server_config (Dict[str, Any]): SSH server configuration

        Returns:
            List[Dict[str, Any]]: List of file information dictionaries
        """
        if not SSH_AVAILABLE:
            self.log("SSH functionality not available. Install paramiko.", level="error")
            return []

        files_found = []
        hostname = server_config.get('hostname', 'unknown')

        try:
            # Create SSH client
            ssh_client = SSHClient()
            ssh_client.set_missing_host_key_policy(AutoAddPolicy())

            # Connection parameters
            connect_params = {
                'hostname': hostname,
                'port': server_config.get('port', 22),
                'username': server_config.get('username'),
                'timeout': self.config['ssh_timeout']
            }

            # Add authentication method
            if 'key_file' in server_config:
                connect_params['key_filename'] = server_config['key_file']
            elif 'password' in server_config:
                connect_params['password'] = server_config['password']

            self.log(f"Connecting to SSH server: {hostname}")
            ssh_client.connect(**connect_params)

            # Create SFTP client
            sftp_client = ssh_client.open_sftp()

            # Scan each directory
            directories = server_config.get('directories', ['/'])
            for directory in directories:
                self.log(f"Scanning remote directory: {hostname}:{directory}")
                dir_files = self.scan_remote_directory(
                    sftp_client, ssh_client, directory, hostname
                )
                files_found.extend(dir_files)

            # Close connections
            sftp_client.close()
            ssh_client.close()

        except Exception as e:
            self.log(f"Error connecting to SSH server {hostname}: {e}", level="error")
            self.errors.append(f"SSH connection error: {hostname} - {str(e)}")
            self.stats[hostname]['errors'] += 1

        self.log(f"Found {len(files_found)} tutorial files on {hostname}")
        return files_found

    def scan_remote_directory(self, sftp_client, ssh_client, directory: str,
                            hostname: str) -> List[Dict[str, Any]]:
        """
        Scan a remote directory via SFTP.

        Args:
            sftp_client: SFTP client
            ssh_client: SSH client
            directory (str): Remote directory path
            hostname (str): Server hostname

        Returns:
            List[Dict[str, Any]]: List of file information dictionaries
        """
        files_found = []
        extensions = self.config['file_extensions']

        try:
            # Build find command for efficiency
            ext_patterns = " -o ".join([f'-name "*.{ext.lstrip(".")}"' for ext in extensions])
            find_command = f'find "{directory}" -type f \\( {ext_patterns} \\) 2>/dev/null'

            # Execute find command
            stdin, stdout, stderr = ssh_client.exec_command(find_command)
            file_paths = stdout.read().decode('utf-8').strip().split('\n')

            # Filter out empty lines
            file_paths = [path.strip() for path in file_paths if path.strip()]

            if not file_paths:
                self.log(f"No tutorial files found in {hostname}:{directory}")
                return files_found

            # Process files with progress bar if available
            if TQDM_AVAILABLE:
                file_iterator = tqdm(file_paths, desc=f"Processing {hostname}:{directory}")
            else:
                file_iterator = file_paths

            for file_path in file_iterator:
                try:
                    file_info = self.get_file_info(
                        file_path, hostname, is_remote=True,
                        sftp_client=sftp_client, ssh_client=ssh_client
                    )
                    if file_info:
                        files_found.append(file_info)
                        self.stats[hostname]['files'] += 1
                        self.stats[hostname]['size'] += file_info['file_size_bytes']

                except Exception as e:
                    self.log(f"Error processing remote file {file_path}: {e}", level="warning")
                    self.errors.append(f"Remote file error: {hostname}:{file_path} - {str(e)}")
                    self.stats[hostname]['errors'] += 1

        except Exception as e:
            self.log(f"Error scanning remote directory {hostname}:{directory}: {e}", level="error")
            self.errors.append(f"Remote directory error: {hostname}:{directory} - {str(e)}")
            self.stats[hostname]['errors'] += 1

        return files_found

    def find_duplicates(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Find duplicate files based on checksum and size.

        Returns:
            Dict[str, List[Dict[str, Any]]]: Dictionary mapping checksums to file lists
        """
        checksum_map = defaultdict(list)

        for file_info in self.files_found:
            checksum = file_info['checksum']
            if checksum != "ERROR" and checksum != "SKIPPED_TOO_LARGE":
                checksum_map[checksum].append(file_info)

        # Filter to only include actual duplicates
        duplicates = {k: v for k, v in checksum_map.items() if len(v) > 1}

        self.log(f"Found {len(duplicates)} groups of duplicate files")
        return duplicates

    def write_csv_report(self, output_file: str = None) -> None:
        """
        Write comprehensive CSV report.

        Args:
            output_file (str): Output file path
        """
        if output_file is None:
            output_file = self.config['output_file']

        separator = self.config['separator']

        try:
            fieldnames = [
                'filename', 'full_path', 'source', 'file_size_bytes', 'file_size_mb',
                'file_size_human', 'file_extension', 'content_type', 'modification_date',
                'checksum', 'hash_algorithm', 'is_duplicate'
            ]

            # Find duplicates
            duplicates = self.find_duplicates()
            duplicate_checksums = set(duplicates.keys())

            # Mark duplicates
            for file_info in self.files_found:
                file_info['is_duplicate'] = file_info['checksum'] in duplicate_checksums

            # Write CSV with custom separator handling
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                if len(separator) == 1:
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames, delimiter=separator)
                    writer.writeheader()
                    writer.writerows(self.files_found)
                else:
                    # Handle multi-character separators
                    csvfile.write(separator.join(fieldnames) + '\n')
                    for file_info in self.files_found:
                        row_values = []
                        for field in fieldnames:
                            value = str(file_info.get(field, ''))
                            if separator in value:
                                value = value.replace(separator, f'\\{separator}')
                            row_values.append(value)
                        csvfile.write(separator.join(row_values) + '\n')

            self.log(f"CSV report written to: {output_file}")

            # Write duplicates report
            if duplicates:
                dup_file = output_file.replace('.csv', '_duplicates.csv')
                self.write_duplicates_report(duplicates, dup_file)

        except Exception as e:
            self.log(f"Error writing CSV report: {e}", level="error")

    def write_duplicates_report(self, duplicates: Dict[str, List[Dict[str, Any]]],
                              output_file: str) -> None:
        """Write detailed duplicates report."""
        separator = self.config['separator']

        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['checksum', 'duplicate_count', 'total_size_mb', 'file_details']

                if len(separator) == 1:
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames, delimiter=separator)
                    writer.writeheader()

                    for checksum, file_list in duplicates.items():
                        total_size = sum(f['file_size_bytes'] for f in file_list)
                        file_details = ' ¦¦¦ '.join([
                            f"{f['source']}:{f['full_path']} ({f['file_size_human']})"
                            for f in file_list
                        ])

                        writer.writerow({
                            'checksum': checksum,
                            'duplicate_count': len(file_list),
                            'total_size_mb': round(total_size / (1024*1024), 2),
                            'file_details': file_details
                        })
                else:
                    # Handle multi-character separators
                    csvfile.write(separator.join(fieldnames) + '\n')

                    for checksum, file_list in duplicates.items():
                        total_size = sum(f['file_size_bytes'] for f in file_list)
                        file_details = ' ¦¦¦ '.join([
                            f"{f['source']}:{f['full_path']} ({f['file_size_human']})"
                            for f in file_list
                        ])

                        # Escape separator in data
                        if separator in file_details:
                            file_details = file_details.replace(separator, f'\\{separator}')

                        row_values = [
                            checksum,
                            str(len(file_list)),
                            str(round(total_size / (1024*1024), 2)),
                            file_details
                        ]
                        csvfile.write(separator.join(row_values) + '\n')

            self.log(f"Duplicates report written to: {output_file}")

        except Exception as e:
            self.log(f"Error writing duplicates report: {e}", level="error")

    def print_summary(self) -> None:
        """Print scan summary statistics."""
        total_files = len(self.files_found)
        total_size = sum(f['file_size_bytes'] for f in self.files_found)
        duplicates = self.find_duplicates()

        print("\n" + "="*60)
        print("TUTORIAL FILE SCAN SUMMARY")
        print("="*60)
        print(f"Total files found: {total_files}")
        print(f"Total size: {self.format_file_size(total_size)}")
        print(f"Duplicate groups: {len(duplicates)}")

        if duplicates:
            dup_files = sum(len(files) for files in duplicates.values())
            dup_size = sum(
                sum(f['file_size_bytes'] for f in files)
                for files in duplicates.values()
            )
            print(f"Duplicate files: {dup_files}")
            print(f"Wasted space: {self.format_file_size(dup_size)}")

        print(f"Errors encountered: {len(self.errors)}")

        print("\nPer-source statistics:")
        for source, stats in self.stats.items():
            print(f"  {source}:")
            print(f"    Files: {stats['files']}")
            print(f"    Size: {self.format_file_size(stats['size'])}")
            print(f"    Errors: {stats['errors']}")

        if self.errors:
            print(f"\nFirst 5 errors:")
            for error in self.errors[:5]:
                print(f"  - {error}")
            if len(self.errors) > 5:
                print(f"  ... and {len(self.errors) - 5} more errors")

    def scan_all(self) -> None:
        """Perform complete scan of all configured locations."""
        self.log("Starting tutorial file scan")
        start_time = datetime.now()

        # Scan local directories
        for directory in self.config['local_directories']:
            local_files = self.scan_local_directory(directory)
            self.files_found.extend(local_files)

        # Scan SSH servers
        for server_config in self.config['ssh_servers']:
            ssh_files = self.scan_ssh_server(server_config)
            self.files_found.extend(ssh_files)

        end_time = datetime.now()
        duration = end_time - start_time

        self.log(f"Scan completed in {duration}")
        self.log(f"Total files found: {len(self.files_found)}")

        # Generate reports
        self.write_csv_report()
        self.print_summary()


def create_sample_config(config_file: str = "tutorial_scanner_config.json") -> None:
    """Create a sample configuration file."""
    sample_config = {
        "ssh_servers": [
            {
                "hostname": "server1.example.com",
                "username": "your_username",
                "key_file": "/path/to/your/private/key",
                "port": 22,
                "directories": [
                    "/home/<USER>/tutorials",
                    "/shared/learning_materials",
                    "/data/courses"
                ]
            },
            {
                "hostname": "*************",
                "username": "admin",
                "password": "your_password",
                "directories": [
                    "/media/tutorials",
                    "/backup/courses"
                ]
            }
        ],
        "local_directories": [
            "/home/<USER>/Downloads",
            "/media/external_drive/tutorials",
            "C:\\Users\\<USER>\\Documents\\Tutorials",
            "D:\\Learning Materials"
        ],
        "file_extensions": [
            ".pdf", ".mp4", ".mkv", ".avi", ".mov", ".wmv",
            ".txt", ".md", ".rst", ".docx", ".pptx", ".epub",
            ".zip", ".rar", ".7z"
        ],
        "output_file": "tutorial_inventory.csv",
        "separator": "|~|",
        "hash_algorithm": "md5",
        "max_file_size_mb": 10000,
        "ssh_timeout": 30,
        "chunk_size": 8192,
        "log_level": "INFO"
    }

    try:
        with open(config_file, 'w') as f:
            json.dump(sample_config, f, indent=2)
        print(f"Sample configuration created: {config_file}")
        print("Please edit this file with your actual server details and paths.")
    except Exception as e:
        print(f"Error creating sample config: {e}")


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="Scan for tutorial files across SSH servers and local drives",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Create sample configuration file
  python tutorial_scanner.py --create-config

  # Run scan with default config
  python tutorial_scanner.py

  # Run scan with custom config
  python tutorial_scanner.py --config my_config.json

  # Run scan with custom output file
  python tutorial_scanner.py --output my_inventory.csv

  # Scan only local directories (no SSH)
  python tutorial_scanner.py --local-only
        """
    )

    parser.add_argument(
        '--config', '-c',
        default='tutorial_scanner_config.json',
        help='Configuration file path (default: tutorial_scanner_config.json)'
    )
    parser.add_argument(
        '--output', '-o',
        help='Output CSV file path (overrides config)'
    )
    parser.add_argument(
        '--create-config',
        action='store_true',
        help='Create a sample configuration file and exit'
    )
    parser.add_argument(
        '--local-only',
        action='store_true',
        help='Scan only local directories (skip SSH servers)'
    )
    parser.add_argument(
        '--separator',
        default='|~|',
        help='CSV separator to use (default: |~|)'
    )

    args = parser.parse_args()

    if args.create_config:
        create_sample_config(args.config)
        return 0

    try:
        # Initialize scanner
        scanner = TutorialScanner(args.config)

        # Override config with command line arguments
        if args.output:
            scanner.config['output_file'] = args.output
        if args.separator:
            scanner.config['separator'] = args.separator
        if args.local_only:
            scanner.config['ssh_servers'] = []

        # Perform scan
        scanner.scan_all()

        print(f"\nScan complete! Check the following files:")
        print(f"  - Main report: {scanner.config['output_file']}")
        print(f"  - Log file: tutorial_scanner.log")

        duplicates_file = scanner.config['output_file'].replace('.csv', '_duplicates.csv')
        if os.path.exists(duplicates_file):
            print(f"  - Duplicates report: {duplicates_file}")

        return 0

    except KeyboardInterrupt:
        print("\nScan interrupted by user")
        return 1
    except Exception as e:
        print(f"Error during scan: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
