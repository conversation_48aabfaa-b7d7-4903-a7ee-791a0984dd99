{"ssh_servers": [{"hostname": "*************", "username": "mint", "password": "mint", "directories": ["/home/<USER>", "/media/"]}, {"hostname": "*************", "username": "mint", "password": "mint", "directories": ["/home/<USER>", "/media/"]}, {"hostname": "*************", "username": "ubuntu", "password": "ubuntu", "directories": ["/home/<USER>", "/media/"]}], "local_directories": ["/home/<USER>/", "/media/"], "file_extensions": [".mp4", ".mkv", ".avi", ".mov", ".wmv", ".zip", ".rar", ".7z"], "output_file": "tutorial_inventory2.csv", "separator": "|~|", "hash_algorithm": "md5", "max_file_size_mb": 10000, "ssh_timeout": 30, "chunk_size": 8192, "log_level": "INFO"}